(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[2],{

/***/ "./resources/scripts/api/server/files/getFileContents.ts":
/*!***************************************************************!*\
  !*** ./resources/scripts/api/server/files/getFileContents.ts ***!
  \***************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\nconst _default = (server, file) => {\n  return new Promise((resolve, reject) => {\n    _api_http__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"a\"].get(\"/api/client/servers/\".concat(server, \"/files/contents\"), {\n      params: {\n        file\n      },\n      transformResponse: res => res,\n      responseType: 'text'\n    }).then(_ref => {\n      let {\n        data\n      } = _ref;\n      return resolve(data);\n    }).catch(reject);\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/api/server/files/getFileContents.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9hcGkvc2VydmVyL2ZpbGVzL2dldEZpbGVDb250ZW50cy50cz9mMDVhIl0sIm5hbWVzIjpbInNlcnZlciIsImZpbGUiLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsImh0dHAiLCJnZXQiLCJwYXJhbXMiLCJ0cmFuc2Zvcm1SZXNwb25zZSIsInJlcyIsInJlc3BvbnNlVHlwZSIsInRoZW4iLCJkYXRhIiwiY2F0Y2giXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7aUJBRWUsQ0FBQ0EsTUFBRCxFQUFpQkMsSUFBakIsS0FBbUQ7RUFDOUQsT0FBTyxJQUFJQyxPQUFKLENBQVksQ0FBQ0MsT0FBRCxFQUFVQyxNQUFWLEtBQXFCO0lBQ3BDQyx5REFBSSxDQUFDQyxHQUFMLCtCQUFnQ04sTUFBaEMsc0JBQXlEO01BQ3JETyxNQUFNLEVBQUU7UUFBRU47TUFBRixDQUQ2QztNQUVyRE8saUJBQWlCLEVBQUdDLEdBQUQsSUFBU0EsR0FGeUI7TUFHckRDLFlBQVksRUFBRTtJQUh1QyxDQUF6RCxFQUtLQyxJQUxMLENBS1U7TUFBQSxJQUFDO1FBQUVDO01BQUYsQ0FBRDtNQUFBLE9BQWNULE9BQU8sQ0FBQ1MsSUFBRCxDQUFyQjtJQUFBLENBTFYsRUFNS0MsS0FOTCxDQU1XVCxNQU5YO0VBT0gsQ0FSTSxDQUFQO0FBU0gsQzs7QUFWYyIsImZpbGUiOiIuL3Jlc291cmNlcy9zY3JpcHRzL2FwaS9zZXJ2ZXIvZmlsZXMvZ2V0RmlsZUNvbnRlbnRzLnRzLmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGh0dHAgZnJvbSAnQC9hcGkvaHR0cCc7XG5cbmV4cG9ydCBkZWZhdWx0IChzZXJ2ZXI6IHN0cmluZywgZmlsZTogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+ID0+IHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4ge1xuICAgICAgICBodHRwLmdldChgL2FwaS9jbGllbnQvc2VydmVycy8ke3NlcnZlcn0vZmlsZXMvY29udGVudHNgLCB7XG4gICAgICAgICAgICBwYXJhbXM6IHsgZmlsZSB9LFxuICAgICAgICAgICAgdHJhbnNmb3JtUmVzcG9uc2U6IChyZXMpID0+IHJlcyxcbiAgICAgICAgICAgIHJlc3BvbnNlVHlwZTogJ3RleHQnLFxuICAgICAgICB9KVxuICAgICAgICAgICAgLnRoZW4oKHsgZGF0YSB9KSA9PiByZXNvbHZlKGRhdGEpKVxuICAgICAgICAgICAgLmNhdGNoKHJlamVjdCk7XG4gICAgfSk7XG59O1xuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./resources/scripts/api/server/files/getFileContents.ts\n");

/***/ }),

/***/ "./resources/scripts/api/server/files/saveFileContents.ts":
/*!****************************************************************!*\
  !*** ./resources/scripts/api/server/files/saveFileContents.ts ***!
  \****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\nconst _default = async (uuid, file, content) => {\n  await _api_http__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"a\"].post(\"/api/client/servers/\".concat(uuid, \"/files/write\"), content, {\n    params: {\n      file\n    },\n    headers: {\n      'Content-Type': 'text/plain'\n    }\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/api/server/files/saveFileContents.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9hcGkvc2VydmVyL2ZpbGVzL3NhdmVGaWxlQ29udGVudHMudHM/ODdlMSJdLCJuYW1lcyI6WyJ1dWlkIiwiZmlsZSIsImNvbnRlbnQiLCJodHRwIiwicG9zdCIsInBhcmFtcyIsImhlYWRlcnMiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7aUJBRWUsT0FBT0EsSUFBUCxFQUFxQkMsSUFBckIsRUFBbUNDLE9BQW5DLEtBQXNFO0VBQ2pGLE1BQU1DLHlEQUFJLENBQUNDLElBQUwsK0JBQWlDSixJQUFqQyxtQkFBcURFLE9BQXJELEVBQThEO0lBQ2hFRyxNQUFNLEVBQUU7TUFBRUo7SUFBRixDQUR3RDtJQUVoRUssT0FBTyxFQUFFO01BQ0wsZ0JBQWdCO0lBRFg7RUFGdUQsQ0FBOUQsQ0FBTjtBQU1ILEM7O0FBUGMiLCJmaWxlIjoiLi9yZXNvdXJjZXMvc2NyaXB0cy9hcGkvc2VydmVyL2ZpbGVzL3NhdmVGaWxlQ29udGVudHMudHMuanMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgaHR0cCBmcm9tICdAL2FwaS9odHRwJztcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHV1aWQ6IHN0cmluZywgZmlsZTogc3RyaW5nLCBjb250ZW50OiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBhd2FpdCBodHRwLnBvc3QoYC9hcGkvY2xpZW50L3NlcnZlcnMvJHt1dWlkfS9maWxlcy93cml0ZWAsIGNvbnRlbnQsIHtcbiAgICAgICAgcGFyYW1zOiB7IGZpbGUgfSxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICd0ZXh0L3BsYWluJyxcbiAgICAgICAgfSxcbiAgICB9KTtcbn07XG4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./resources/scripts/api/server/files/saveFileContents.ts\n");

/***/ }),

/***/ "./resources/scripts/components/elements/CodemirrorEditor.tsx":
/*!********************************************************************!*\
  !*** ./resources/scripts/components/elements/CodemirrorEditor.tsx ***!
  \********************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var codemirror__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! codemirror */ \"./node_modules/codemirror/lib/codemirror.js\");\n/* harmony import */ var codemirror__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(codemirror__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _modes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/modes */ \"./resources/scripts/modes.ts\");\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n__webpack_require__(/*! codemirror/lib/codemirror.css */ \"./node_modules/codemirror/lib/codemirror.css\");\n\n__webpack_require__(/*! codemirror/theme/ayu-mirage.css */ \"./node_modules/codemirror/theme/ayu-mirage.css\");\n\n__webpack_require__(/*! codemirror/addon/edit/closebrackets */ \"./node_modules/codemirror/addon/edit/closebrackets.js\");\n\n__webpack_require__(/*! codemirror/addon/edit/closetag */ \"./node_modules/codemirror/addon/edit/closetag.js\");\n\n__webpack_require__(/*! codemirror/addon/edit/matchbrackets */ \"./node_modules/codemirror/addon/edit/matchbrackets.js\");\n\n__webpack_require__(/*! codemirror/addon/edit/matchtags */ \"./node_modules/codemirror/addon/edit/matchtags.js\");\n\n__webpack_require__(/*! codemirror/addon/edit/trailingspace */ \"./node_modules/codemirror/addon/edit/trailingspace.js\");\n\n__webpack_require__(/*! codemirror/addon/fold/foldcode */ \"./node_modules/codemirror/addon/fold/foldcode.js\");\n\n__webpack_require__(/*! codemirror/addon/fold/foldgutter.css */ \"./node_modules/codemirror/addon/fold/foldgutter.css\");\n\n__webpack_require__(/*! codemirror/addon/fold/foldgutter */ \"./node_modules/codemirror/addon/fold/foldgutter.js\");\n\n__webpack_require__(/*! codemirror/addon/fold/brace-fold */ \"./node_modules/codemirror/addon/fold/brace-fold.js\");\n\n__webpack_require__(/*! codemirror/addon/fold/comment-fold */ \"./node_modules/codemirror/addon/fold/comment-fold.js\");\n\n__webpack_require__(/*! codemirror/addon/fold/indent-fold */ \"./node_modules/codemirror/addon/fold/indent-fold.js\");\n\n__webpack_require__(/*! codemirror/addon/fold/markdown-fold */ \"./node_modules/codemirror/addon/fold/markdown-fold.js\");\n\n__webpack_require__(/*! codemirror/addon/fold/xml-fold */ \"./node_modules/codemirror/addon/fold/xml-fold.js\");\n\n__webpack_require__(/*! codemirror/addon/hint/css-hint */ \"./node_modules/codemirror/addon/hint/css-hint.js\");\n\n__webpack_require__(/*! codemirror/addon/hint/html-hint */ \"./node_modules/codemirror/addon/hint/html-hint.js\");\n\n__webpack_require__(/*! codemirror/addon/hint/javascript-hint */ \"./node_modules/codemirror/addon/hint/javascript-hint.js\");\n\n__webpack_require__(/*! codemirror/addon/hint/show-hint.css */ \"./node_modules/codemirror/addon/hint/show-hint.css\");\n\n__webpack_require__(/*! codemirror/addon/hint/show-hint */ \"./node_modules/codemirror/addon/hint/show-hint.js\");\n\n__webpack_require__(/*! codemirror/addon/hint/sql-hint */ \"./node_modules/codemirror/addon/hint/sql-hint.js\");\n\n__webpack_require__(/*! codemirror/addon/hint/xml-hint */ \"./node_modules/codemirror/addon/hint/xml-hint.js\");\n\n__webpack_require__(/*! codemirror/addon/mode/simple */ \"./node_modules/codemirror/addon/mode/simple.js\");\n\n__webpack_require__(/*! codemirror/addon/dialog/dialog.css */ \"./node_modules/codemirror/addon/dialog/dialog.css\");\n\n__webpack_require__(/*! codemirror/addon/dialog/dialog */ \"./node_modules/codemirror/addon/dialog/dialog.js\");\n\n__webpack_require__(/*! codemirror/addon/scroll/annotatescrollbar */ \"./node_modules/codemirror/addon/scroll/annotatescrollbar.js\");\n\n__webpack_require__(/*! codemirror/addon/scroll/scrollpastend */ \"./node_modules/codemirror/addon/scroll/scrollpastend.js\");\n\n__webpack_require__(/*! codemirror/addon/scroll/simplescrollbars.css */ \"./node_modules/codemirror/addon/scroll/simplescrollbars.css\");\n\n__webpack_require__(/*! codemirror/addon/scroll/simplescrollbars */ \"./node_modules/codemirror/addon/scroll/simplescrollbars.js\");\n\n__webpack_require__(/*! codemirror/addon/search/jump-to-line */ \"./node_modules/codemirror/addon/search/jump-to-line.js\");\n\n__webpack_require__(/*! codemirror/addon/search/match-highlighter */ \"./node_modules/codemirror/addon/search/match-highlighter.js\");\n\n__webpack_require__(/*! codemirror/addon/search/matchesonscrollbar.css */ \"./node_modules/codemirror/addon/search/matchesonscrollbar.css\");\n\n__webpack_require__(/*! codemirror/addon/search/matchesonscrollbar */ \"./node_modules/codemirror/addon/search/matchesonscrollbar.js\");\n\n__webpack_require__(/*! codemirror/addon/search/search */ \"./node_modules/codemirror/addon/search/search.js\");\n\n__webpack_require__(/*! codemirror/addon/search/searchcursor */ \"./node_modules/codemirror/addon/search/searchcursor.js\");\n\n__webpack_require__(/*! codemirror/mode/brainfuck/brainfuck */ \"./node_modules/codemirror/mode/brainfuck/brainfuck.js\");\n\n__webpack_require__(/*! codemirror/mode/clike/clike */ \"./node_modules/codemirror/mode/clike/clike.js\");\n\n__webpack_require__(/*! codemirror/mode/css/css */ \"./node_modules/codemirror/mode/css/css.js\");\n\n__webpack_require__(/*! codemirror/mode/dart/dart */ \"./node_modules/codemirror/mode/dart/dart.js\");\n\n__webpack_require__(/*! codemirror/mode/diff/diff */ \"./node_modules/codemirror/mode/diff/diff.js\");\n\n__webpack_require__(/*! codemirror/mode/dockerfile/dockerfile */ \"./node_modules/codemirror/mode/dockerfile/dockerfile.js\");\n\n__webpack_require__(/*! codemirror/mode/erlang/erlang */ \"./node_modules/codemirror/mode/erlang/erlang.js\");\n\n__webpack_require__(/*! codemirror/mode/gfm/gfm */ \"./node_modules/codemirror/mode/gfm/gfm.js\");\n\n__webpack_require__(/*! codemirror/mode/go/go */ \"./node_modules/codemirror/mode/go/go.js\");\n\n__webpack_require__(/*! codemirror/mode/handlebars/handlebars */ \"./node_modules/codemirror/mode/handlebars/handlebars.js\");\n\n__webpack_require__(/*! codemirror/mode/htmlembedded/htmlembedded */ \"./node_modules/codemirror/mode/htmlembedded/htmlembedded.js\");\n\n__webpack_require__(/*! codemirror/mode/htmlmixed/htmlmixed */ \"./node_modules/codemirror/mode/htmlmixed/htmlmixed.js\");\n\n__webpack_require__(/*! codemirror/mode/http/http */ \"./node_modules/codemirror/mode/http/http.js\");\n\n__webpack_require__(/*! codemirror/mode/javascript/javascript */ \"./node_modules/codemirror/mode/javascript/javascript.js\");\n\n__webpack_require__(/*! codemirror/mode/jsx/jsx */ \"./node_modules/codemirror/mode/jsx/jsx.js\");\n\n__webpack_require__(/*! codemirror/mode/julia/julia */ \"./node_modules/codemirror/mode/julia/julia.js\");\n\n__webpack_require__(/*! codemirror/mode/lua/lua */ \"./node_modules/codemirror/mode/lua/lua.js\");\n\n__webpack_require__(/*! codemirror/mode/markdown/markdown */ \"./node_modules/codemirror/mode/markdown/markdown.js\");\n\n__webpack_require__(/*! codemirror/mode/nginx/nginx */ \"./node_modules/codemirror/mode/nginx/nginx.js\");\n\n__webpack_require__(/*! codemirror/mode/perl/perl */ \"./node_modules/codemirror/mode/perl/perl.js\");\n\n__webpack_require__(/*! codemirror/mode/php/php */ \"./node_modules/codemirror/mode/php/php.js\");\n\n__webpack_require__(/*! codemirror/mode/properties/properties */ \"./node_modules/codemirror/mode/properties/properties.js\");\n\n__webpack_require__(/*! codemirror/mode/protobuf/protobuf */ \"./node_modules/codemirror/mode/protobuf/protobuf.js\");\n\n__webpack_require__(/*! codemirror/mode/pug/pug */ \"./node_modules/codemirror/mode/pug/pug.js\");\n\n__webpack_require__(/*! codemirror/mode/python/python */ \"./node_modules/codemirror/mode/python/python.js\");\n\n__webpack_require__(/*! codemirror/mode/rpm/rpm */ \"./node_modules/codemirror/mode/rpm/rpm.js\");\n\n__webpack_require__(/*! codemirror/mode/ruby/ruby */ \"./node_modules/codemirror/mode/ruby/ruby.js\");\n\n__webpack_require__(/*! codemirror/mode/rust/rust */ \"./node_modules/codemirror/mode/rust/rust.js\");\n\n__webpack_require__(/*! codemirror/mode/sass/sass */ \"./node_modules/codemirror/mode/sass/sass.js\");\n\n__webpack_require__(/*! codemirror/mode/shell/shell */ \"./node_modules/codemirror/mode/shell/shell.js\");\n\n__webpack_require__(/*! codemirror/mode/smarty/smarty */ \"./node_modules/codemirror/mode/smarty/smarty.js\");\n\n__webpack_require__(/*! codemirror/mode/sql/sql */ \"./node_modules/codemirror/mode/sql/sql.js\");\n\n__webpack_require__(/*! codemirror/mode/swift/swift */ \"./node_modules/codemirror/mode/swift/swift.js\");\n\n__webpack_require__(/*! codemirror/mode/toml/toml */ \"./node_modules/codemirror/mode/toml/toml.js\");\n\n__webpack_require__(/*! codemirror/mode/twig/twig */ \"./node_modules/codemirror/mode/twig/twig.js\");\n\n__webpack_require__(/*! codemirror/mode/vue/vue */ \"./node_modules/codemirror/mode/vue/vue.js\");\n\n__webpack_require__(/*! codemirror/mode/xml/xml */ \"./node_modules/codemirror/mode/xml/xml.js\");\n\n__webpack_require__(/*! codemirror/mode/yaml/yaml */ \"./node_modules/codemirror/mode/yaml/yaml.js\");\n\nconst EditorContainer = /*#__PURE__*/styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"].div.withConfig({\n  displayName: \"CodemirrorEditor__EditorContainer\",\n  componentId: \"sc-1dzlt6m-0\"\n})([\"min-height:16rem;height:calc(100vh - 20rem);\", \";> div{\", \";}.CodeMirror{font-size:12px;line-height:1.375rem;}.CodeMirror-linenumber{padding:1px 12px 0 12px !important;}.CodeMirror-foldmarker{color:#cbccc6;text-shadow:none;margin-left:0.25rem;margin-right:0.25rem;}\"], {\n  \"position\": \"relative\"\n}, {\n  \"borderRadius\": \"0.25rem\",\n  \"height\": \"100%\"\n});\n\nconst findModeByFilename = filename => {\n  for (let i = 0; i < _modes__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"a\"].length; i++) {\n    const info = _modes__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"a\"][i];\n\n    if (info.file && info.file.test(filename)) {\n      return info;\n    }\n  }\n\n  const dot = filename.lastIndexOf('.');\n  const ext = dot > -1 && filename.substring(dot + 1, filename.length);\n\n  if (ext) {\n    for (let i = 0; i < _modes__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"a\"].length; i++) {\n      const info = _modes__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"a\"][i];\n\n      if (info.ext) {\n        for (let j = 0; j < info.ext.length; j++) {\n          if (info.ext[j] === ext) {\n            return info;\n          }\n        }\n      }\n    }\n  }\n\n  return undefined;\n};\n\nconst _default = _ref => {\n  let {\n    style,\n    initialContent,\n    filename,\n    mode,\n    fetchContent,\n    onContentSaved,\n    onModeChanged\n  } = _ref;\n  const [editor, setEditor] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])();\n  const ref = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useCallback\"])(node => {\n    if (!node) return;\n    const e = codemirror__WEBPACK_IMPORTED_MODULE_2___default.a.fromTextArea(node, {\n      mode: 'text/plain',\n      theme: 'ayu-mirage',\n      indentUnit: 4,\n      smartIndent: true,\n      tabSize: 4,\n      indentWithTabs: false,\n      lineWrapping: true,\n      lineNumbers: true,\n      foldGutter: true,\n      fixedGutter: true,\n      scrollbarStyle: 'overlay',\n      coverGutterNextToScrollbar: false,\n      readOnly: false,\n      showCursorWhenSelecting: false,\n      autofocus: false,\n      spellcheck: true,\n      autocorrect: false,\n      autocapitalize: false,\n      lint: false,\n      // @ts-expect-error this property is actually used, the d.ts file for CodeMirror is incorrect.\n      autoCloseBrackets: true,\n      matchBrackets: true,\n      gutters: ['CodeMirror-linenumbers', 'CodeMirror-foldgutter']\n    });\n    setEditor(e);\n  }, []);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    var _findModeByFilename;\n\n    if (filename === undefined) {\n      return;\n    }\n\n    onModeChanged(((_findModeByFilename = findModeByFilename(filename)) === null || _findModeByFilename === void 0 ? void 0 : _findModeByFilename.mime) || 'text/plain');\n  }, [filename]);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    editor && editor.setOption('mode', mode);\n  }, [editor, mode]);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    editor && editor.setValue(initialContent || '');\n  }, [editor, initialContent]);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    if (!editor) {\n      fetchContent(() => Promise.reject(new Error('no editor session has been configured')));\n      return;\n    }\n\n    editor.addKeyMap({\n      'Ctrl-S': () => onContentSaved(),\n      'Cmd-S': () => onContentSaved()\n    });\n    fetchContent(() => Promise.resolve(editor.getValue()));\n  }, [editor, fetchContent, onContentSaved]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(EditorContainer, {\n    style: style\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(\"textarea\", {\n    ref: ref\n  }));\n};\n\n__signature__(_default, \"useState{[editor, setEditor]}\\nuseCallback{ref}\\nuseEffect{}\\nuseEffect{}\\nuseEffect{}\\nuseEffect{}\");\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(EditorContainer, \"EditorContainer\", \"/workspaces/Pterod/resources/scripts/components/elements/CodemirrorEditor.tsx\");\n  reactHotLoader.register(findModeByFilename, \"findModeByFilename\", \"/workspaces/Pterod/resources/scripts/components/elements/CodemirrorEditor.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/elements/CodemirrorEditor.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/elements/CodemirrorEditor.tsx\n");

/***/ }),

/***/ "./resources/scripts/components/server/files/FileEditContainer.tsx":
/*!*************************************************************************!*\
  !*** ./resources/scripts/components/server/files/FileEditContainer.tsx ***!
  \*************************************************************************/
/*! exports provided: default */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_server_files_getFileContents__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/api/server/files/getFileContents */ \"./resources/scripts/api/server/files/getFileContents.ts\");\n/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n/* harmony import */ var _components_elements_SpinnerOverlay__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/elements/SpinnerOverlay */ \"./resources/scripts/components/elements/SpinnerOverlay.tsx\");\n/* harmony import */ var _api_server_files_saveFileContents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/api/server/files/saveFileContents */ \"./resources/scripts/api/server/files/saveFileContents.ts\");\n/* harmony import */ var _components_server_files_FileManagerBreadcrumbs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/server/files/FileManagerBreadcrumbs */ \"./resources/scripts/components/server/files/FileManagerBreadcrumbs.tsx\");\n/* harmony import */ var react_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-router */ \"./node_modules/react-router/esm/react-router.js\");\n/* harmony import */ var _components_server_files_FileNameModal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/server/files/FileNameModal */ \"./resources/scripts/components/server/files/FileNameModal.tsx\");\n/* harmony import */ var _components_elements_Can__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/elements/Can */ \"./resources/scripts/components/elements/Can.tsx\");\n/* harmony import */ var _components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/FlashMessageRender */ \"./resources/scripts/components/FlashMessageRender.tsx\");\n/* harmony import */ var _components_elements_PageContentBlock__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/elements/PageContentBlock */ \"./resources/scripts/components/elements/PageContentBlock.tsx\");\n/* harmony import */ var _components_elements_ScreenBlock__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/elements/ScreenBlock */ \"./resources/scripts/components/elements/ScreenBlock.tsx\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n/* harmony import */ var _components_elements_Select__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/elements/Select */ \"./resources/scripts/components/elements/Select.tsx\");\n/* harmony import */ var _modes__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/modes */ \"./resources/scripts/modes.ts\");\n/* harmony import */ var _plugins_useFlash__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/plugins/useFlash */ \"./resources/scripts/plugins/useFlash.ts\");\n/* harmony import */ var _state_server__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/state/server */ \"./resources/scripts/state/server/index.ts\");\n/* harmony import */ var _components_elements_ErrorBoundary__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/elements/ErrorBoundary */ \"./resources/scripts/components/elements/ErrorBoundary.tsx\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/helpers */ \"./resources/scripts/helpers.ts\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! path */ \"./node_modules/path-browserify/index.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var _components_elements_CodemirrorEditor__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/components/elements/CodemirrorEditor */ \"./resources/scripts/components/elements/CodemirrorEditor.tsx\");\n/* harmony import */ var _components_elements_Switch__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/elements/Switch */ \"./resources/scripts/components/elements/Switch.tsx\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst _default = () => {\n  const [error, setError] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])('');\n  const {\n    action\n  } = Object(react_router__WEBPACK_IMPORTED_MODULE_7__[/* useParams */ \"i\"])();\n  const [loading, setLoading] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(action === 'edit');\n  const [content, setContent] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])('');\n  const [modalVisible, setModalVisible] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const [mode, setMode] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])('text/plain');\n  const history = Object(react_router__WEBPACK_IMPORTED_MODULE_7__[/* useHistory */ \"g\"])();\n  const {\n    hash\n  } = Object(react_router__WEBPACK_IMPORTED_MODULE_7__[/* useLocation */ \"h\"])();\n  const id = _state_server__WEBPACK_IMPORTED_MODULE_17__[/* ServerContext */ \"a\"].useStoreState(state => state.server.data.id);\n  const uuid = _state_server__WEBPACK_IMPORTED_MODULE_17__[/* ServerContext */ \"a\"].useStoreState(state => state.server.data.uuid);\n  const setDirectory = _state_server__WEBPACK_IMPORTED_MODULE_17__[/* ServerContext */ \"a\"].useStoreActions(actions => actions.files.setDirectory);\n  const {\n    addError,\n    clearFlashes\n  } = Object(_plugins_useFlash__WEBPACK_IMPORTED_MODULE_16__[/* default */ \"a\"])();\n  let fetchFileContent = null;\n  const isServerProperties = hash.replace(/^#/, '') === '/server.properties';\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    if (action === 'new') return;\n    setError('');\n    setLoading(true);\n    const path = Object(_helpers__WEBPACK_IMPORTED_MODULE_19__[/* hashToPath */ \"d\"])(hash);\n    setDirectory(Object(path__WEBPACK_IMPORTED_MODULE_20__[\"dirname\"])(path));\n    Object(_api_server_files_getFileContents__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"a\"])(uuid, path).then(setContent).catch(error => {\n      console.error(error);\n      setError(Object(_api_http__WEBPACK_IMPORTED_MODULE_3__[/* httpErrorToHuman */ \"c\"])(error));\n    }).then(() => setLoading(false));\n  }, [action, uuid, hash]);\n\n  const save = name => {\n    if (!fetchFileContent) {\n      return;\n    }\n\n    setLoading(true);\n    clearFlashes('files:view');\n    fetchFileContent().then(content => Object(_api_server_files_saveFileContents__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"])(uuid, name || Object(_helpers__WEBPACK_IMPORTED_MODULE_19__[/* hashToPath */ \"d\"])(hash), content)).then(() => {\n      if (name) {\n        history.push(\"/server/\".concat(id, \"/files/edit#/\").concat(Object(_helpers__WEBPACK_IMPORTED_MODULE_19__[/* encodePathSegments */ \"b\"])(name)));\n        return;\n      }\n\n      return Promise.resolve();\n    }).catch(error => {\n      console.error(error);\n      addError({\n        message: Object(_api_http__WEBPACK_IMPORTED_MODULE_3__[/* httpErrorToHuman */ \"c\"])(error),\n        key: 'files:view'\n      });\n    }).then(() => setLoading(false));\n  };\n\n  if (error) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_ScreenBlock__WEBPACK_IMPORTED_MODULE_12__[/* ServerError */ \"b\"], {\n      message: error,\n      onBack: () => history.goBack()\n    });\n  }\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_PageContentBlock__WEBPACK_IMPORTED_MODULE_11__[/* default */ \"a\"], null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledFlashMessageRender, {\n    byKey: 'files:view',\n    \"data-tw\": \"mb-4\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_ErrorBoundary__WEBPACK_IMPORTED_MODULE_18__[/* default */ \"a\"], null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv, {\n    \"data-tw\": \"flex justify-between items-center mb-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_server_files_FileManagerBreadcrumbs__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"], {\n    withinFileEditor: true,\n    isNewFile: action !== 'edit'\n  }), isServerProperties && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv2, {\n    \"data-tw\": \"flex items-center space-x-2 text-sm\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Switch__WEBPACK_IMPORTED_MODULE_22__[/* default */ \"a\"], {\n    name: 'config-editor',\n    label: 'Config Editor',\n    defaultChecked: true,\n    onChange: () => {\n      history.push(\"/server/\".concat(id, \"/properties\"));\n    }\n  })))), hash.replace(/^#/, '').endsWith('.pteroignore') && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv3, {\n    \"data-tw\": \"mb-4 p-4 border-l-4 bg-neutral-900 rounded border-cyan-400\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP, {\n    \"data-tw\": \"text-neutral-300 text-sm\"\n  }, \"You're editing a \", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledCode, {\n    \"data-tw\": \"font-mono bg-black rounded py-px px-1\"\n  }, \".pteroignore\"), ' ', \"file. Any files or directories listed in here will be excluded from backups. Wildcards are supported by using an asterisk (\", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledCode2, {\n    \"data-tw\": \"font-mono bg-black rounded py-px px-1\"\n  }, \"*\"), \"). You can negate a prior rule by prepending an exclamation point (\", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledCode3, {\n    \"data-tw\": \"font-mono bg-black rounded py-px px-1\"\n  }, \"!\"), \").\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_server_files_FileNameModal__WEBPACK_IMPORTED_MODULE_8__[/* default */ \"a\"], {\n    visible: modalVisible,\n    onDismissed: () => setModalVisible(false),\n    onFileNamed: name => {\n      setModalVisible(false);\n      save(name);\n    }\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv4, {\n    \"data-tw\": \"relative\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_SpinnerOverlay__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"], {\n    visible: loading\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_CodemirrorEditor__WEBPACK_IMPORTED_MODULE_21__[/* default */ \"a\"], {\n    mode: mode,\n    filename: hash.replace(/^#/, ''),\n    onModeChanged: setMode,\n    initialContent: content,\n    fetchContent: value => {\n      fetchFileContent = value;\n    },\n    onContentSaved: () => {\n      if (action !== 'edit') {\n        setModalVisible(true);\n      } else {\n        save();\n      }\n    }\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv5, {\n    \"data-tw\": \"flex justify-end mt-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv6, {\n    \"data-tw\": \"flex-1 sm:flex-none rounded bg-neutral-900 mr-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Select__WEBPACK_IMPORTED_MODULE_14__[/* default */ \"a\"], {\n    value: mode,\n    onChange: e => setMode(e.currentTarget.value)\n  }, _modes__WEBPACK_IMPORTED_MODULE_15__[/* default */ \"a\"].map(mode => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(\"option\", {\n    key: \"\".concat(mode.name, \"_\").concat(mode.mime),\n    value: mode.mime\n  }, mode.name)))), action === 'edit' ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Can__WEBPACK_IMPORTED_MODULE_9__[/* default */ \"a\"], {\n    action: 'file.update'\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton, {\n    onClick: () => save(),\n    \"data-tw\": \"flex-1 sm:flex-none\"\n  }, \"Save Content\")) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Can__WEBPACK_IMPORTED_MODULE_9__[/* default */ \"a\"], {\n    action: 'file.create'\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton2, {\n    onClick: () => setModalVisible(true),\n    \"data-tw\": \"flex-1 sm:flex-none\"\n  }, \"Create File\"))));\n};\n\n__signature__(_default, \"useState{[error, setError]('')}\\nuseParams{{ action }}\\nuseState{[loading, setLoading](action === 'edit')}\\nuseState{[content, setContent]('')}\\nuseState{[modalVisible, setModalVisible](false)}\\nuseState{[mode, setMode]('text/plain')}\\nuseHistory{history}\\nuseLocation{{ hash }}\\nuseStoreState{id}\\nuseStoreState{uuid}\\nuseStoreActions{setDirectory}\\nuseFlash{{ addError, clearFlashes }}\\nuseEffect{}\", () => [react_router__WEBPACK_IMPORTED_MODULE_7__[/* useParams */ \"i\"], react_router__WEBPACK_IMPORTED_MODULE_7__[/* useHistory */ \"g\"], react_router__WEBPACK_IMPORTED_MODULE_7__[/* useLocation */ \"h\"], _plugins_useFlash__WEBPACK_IMPORTED_MODULE_16__[/* default */ \"a\"]]);\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_default);\n\nvar _StyledFlashMessageRender = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_10__[/* default */ \"a\"]).withConfig({\n  displayName: \"FileEditContainer___StyledFlashMessageRender\",\n  componentId: \"sc-48rzpu-0\"\n})({\n  \"marginBottom\": \"1rem\"\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"FileEditContainer___StyledDiv\",\n  componentId: \"sc-48rzpu-1\"\n})({\n  \"display\": \"flex\",\n  \"justifyContent\": \"space-between\",\n  \"alignItems\": \"center\",\n  \"marginBottom\": \"1rem\"\n});\n\nvar _StyledDiv2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"FileEditContainer___StyledDiv2\",\n  componentId: \"sc-48rzpu-2\"\n})({\n  \"display\": \"flex\",\n  \"alignItems\": \"center\",\n  \"> :not([hidden]) ~ :not([hidden])\": {\n    \"--tw-space-x-reverse\": 0,\n    \"marginRight\": \"calc(0.5rem * var(--tw-space-x-reverse))\",\n    \"marginLeft\": \"calc(0.5rem * calc(1 - var(--tw-space-x-reverse)))\"\n  },\n  \"fontSize\": \"0.875rem\",\n  \"lineHeight\": \"1.25rem\"\n});\n\nvar _StyledDiv3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"FileEditContainer___StyledDiv3\",\n  componentId: \"sc-48rzpu-3\"\n})({\n  \"marginBottom\": \"1rem\",\n  \"padding\": \"1rem\",\n  \"borderLeftWidth\": \"4px\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(210, 24%, 16%, var(--tw-bg-opacity))\",\n  \"borderRadius\": \"0.25rem\",\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"rgba(34, 211, 238, var(--tw-border-opacity))\"\n});\n\nvar _StyledP = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"FileEditContainer___StyledP\",\n  componentId: \"sc-48rzpu-4\"\n})({\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(211, 13%, 65%, var(--tw-text-opacity))\",\n  \"fontSize\": \"0.875rem\",\n  \"lineHeight\": \"1.25rem\"\n});\n\nvar _StyledCode = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"code\").withConfig({\n  displayName: \"FileEditContainer___StyledCode\",\n  componentId: \"sc-48rzpu-5\"\n})({\n  \"fontFamily\": \"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"rgba(19, 26, 32, var(--tw-bg-opacity))\",\n  \"borderRadius\": \"0.25rem\",\n  \"paddingTop\": \"1px\",\n  \"paddingBottom\": \"1px\",\n  \"paddingLeft\": \"0.25rem\",\n  \"paddingRight\": \"0.25rem\"\n});\n\nvar _StyledCode2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"code\").withConfig({\n  displayName: \"FileEditContainer___StyledCode2\",\n  componentId: \"sc-48rzpu-6\"\n})({\n  \"fontFamily\": \"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"rgba(19, 26, 32, var(--tw-bg-opacity))\",\n  \"borderRadius\": \"0.25rem\",\n  \"paddingTop\": \"1px\",\n  \"paddingBottom\": \"1px\",\n  \"paddingLeft\": \"0.25rem\",\n  \"paddingRight\": \"0.25rem\"\n});\n\nvar _StyledCode3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"code\").withConfig({\n  displayName: \"FileEditContainer___StyledCode3\",\n  componentId: \"sc-48rzpu-7\"\n})({\n  \"fontFamily\": \"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"rgba(19, 26, 32, var(--tw-bg-opacity))\",\n  \"borderRadius\": \"0.25rem\",\n  \"paddingTop\": \"1px\",\n  \"paddingBottom\": \"1px\",\n  \"paddingLeft\": \"0.25rem\",\n  \"paddingRight\": \"0.25rem\"\n});\n\nvar _StyledDiv4 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"FileEditContainer___StyledDiv4\",\n  componentId: \"sc-48rzpu-8\"\n})({\n  \"position\": \"relative\"\n});\n\nvar _StyledDiv5 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"FileEditContainer___StyledDiv5\",\n  componentId: \"sc-48rzpu-9\"\n})({\n  \"display\": \"flex\",\n  \"justifyContent\": \"flex-end\",\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledDiv6 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"FileEditContainer___StyledDiv6\",\n  componentId: \"sc-48rzpu-10\"\n})({\n  \"flex\": \"1 1 0%\",\n  \"borderRadius\": \"0.25rem\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(210, 24%, 16%, var(--tw-bg-opacity))\",\n  \"marginRight\": \"1rem\",\n  \"@media (min-width: 640px)\": {\n    \"flex\": \"none\"\n  }\n});\n\nvar _StyledButton = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_13__[/* default */ \"a\"]).withConfig({\n  displayName: \"FileEditContainer___StyledButton\",\n  componentId: \"sc-48rzpu-11\"\n})({\n  \"flex\": \"1 1 0%\",\n  \"@media (min-width: 640px)\": {\n    \"flex\": \"none\"\n  }\n});\n\nvar _StyledButton2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_13__[/* default */ \"a\"]).withConfig({\n  displayName: \"FileEditContainer___StyledButton2\",\n  componentId: \"sc-48rzpu-12\"\n})({\n  \"flex\": \"1 1 0%\",\n  \"@media (min-width: 640px)\": {\n    \"flex\": \"none\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/server/files/FileEditContainer.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/server/files/FileEditContainer.tsx\n");

/***/ }),

/***/ "./resources/scripts/components/server/files/FileNameModal.tsx":
/*!*********************************************************************!*\
  !*** ./resources/scripts/components/server/files/FileNameModal.tsx ***!
  \*********************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_elements_Modal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/elements/Modal */ \"./resources/scripts/components/elements/Modal.tsx\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! formik */ \"./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! yup */ \"./node_modules/yup/es/index.js\");\n/* harmony import */ var _components_elements_Field__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/elements/Field */ \"./resources/scripts/components/elements/Field.tsx\");\n/* harmony import */ var _state_server__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/state/server */ \"./resources/scripts/state/server/index.ts\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! path */ \"./node_modules/path-browserify/index.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\nconst _default = _ref => {\n  let {\n    onFileNamed,\n    onDismissed\n  } = _ref,\n      props = _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1___default()(_ref, [\"onFileNamed\", \"onDismissed\"]);\n\n  const directory = _state_server__WEBPACK_IMPORTED_MODULE_8__[/* ServerContext */ \"a\"].useStoreState(state => state.files.directory);\n\n  const submit = (values, _ref2) => {\n    let {\n      setSubmitting\n    } = _ref2;\n    onFileNamed(Object(path__WEBPACK_IMPORTED_MODULE_9__[\"join\"])(directory, values.fileName));\n    setSubmitting(false);\n  };\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(formik__WEBPACK_IMPORTED_MODULE_5__[/* Formik */ \"c\"], {\n    onSubmit: submit,\n    initialValues: {\n      fileName: ''\n    },\n    validationSchema: Object(yup__WEBPACK_IMPORTED_MODULE_6__[/* object */ \"d\"])().shape({\n      fileName: Object(yup__WEBPACK_IMPORTED_MODULE_6__[/* string */ \"f\"])().required().min(1)\n    })\n  }, _ref3 => {\n    let {\n      resetForm\n    } = _ref3;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_components_elements_Modal__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"b\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      onDismissed: () => {\n        resetForm();\n        onDismissed();\n      }\n    }, props), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(formik__WEBPACK_IMPORTED_MODULE_5__[/* Form */ \"b\"], null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_components_elements_Field__WEBPACK_IMPORTED_MODULE_7__[/* default */ \"a\"], {\n      id: 'fileName',\n      name: 'fileName',\n      label: 'File Name',\n      description: 'Enter the name that this file should be saved as.',\n      autoFocus: true\n    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_StyledDiv, {\n      \"data-tw\": \"mt-6 text-right\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_components_elements_Button__WEBPACK_IMPORTED_MODULE_10__[/* default */ \"a\"], null, \"Create File\"))));\n  });\n};\n\n__signature__(_default, \"useStoreState{directory}\");\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"FileNameModal___StyledDiv\",\n  componentId: \"sc-11rjmp3-0\"\n})({\n  \"marginTop\": \"1.5rem\",\n  \"textAlign\": \"right\"\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/server/files/FileNameModal.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/server/files/FileNameModal.tsx\n");

/***/ }),

/***/ "./resources/scripts/modes.ts":
/*!************************************!*\
  !*** ./resources/scripts/modes.ts ***!
  \************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\nconst modes = [{\n  name: 'C',\n  mime: 'text/x-csrc',\n  mode: 'clike',\n  ext: ['c', 'h', 'ino']\n}, {\n  name: 'C++',\n  mime: 'text/x-c++src',\n  mode: 'clike',\n  ext: ['cpp', 'c++', 'cc', 'cxx', 'hpp', 'h++', 'hh', 'hxx'],\n  alias: ['cpp']\n}, {\n  name: 'C#',\n  mime: 'text/x-csharp',\n  mode: 'clike',\n  ext: ['cs'],\n  alias: ['csharp', 'cs']\n}, {\n  name: 'CSS',\n  mime: 'text/css',\n  mode: 'css',\n  ext: ['css']\n}, {\n  name: 'CQL',\n  mime: 'text/x-cassandra',\n  mode: 'sql',\n  ext: ['cql']\n}, {\n  name: 'Diff',\n  mime: 'text/x-diff',\n  mode: 'diff',\n  ext: ['diff', 'patch']\n}, {\n  name: 'Dockerfile',\n  mime: 'text/x-dockerfile',\n  mode: 'dockerfile',\n  file: /^Dockerfile$/\n}, {\n  name: 'Git Markdown',\n  mime: 'text/x-gfm',\n  mode: 'gfm',\n  file: /^(readme|contributing|history|license).md$/i\n}, {\n  name: 'Golang',\n  mime: 'text/x-go',\n  mode: 'go',\n  ext: ['go']\n}, {\n  name: 'HTML',\n  mime: 'text/html',\n  mode: 'htmlmixed',\n  ext: ['html', 'htm', 'handlebars', 'hbs'],\n  alias: ['xhtml']\n}, {\n  name: 'HTTP',\n  mime: 'message/http',\n  mode: 'http'\n}, {\n  name: 'JavaScript',\n  mime: 'text/javascript',\n  mimes: ['text/javascript', 'text/ecmascript', 'application/javascript', 'application/x-javascript', 'application/ecmascript'],\n  mode: 'javascript',\n  ext: ['js'],\n  alias: ['ecmascript', 'js', 'node']\n}, {\n  name: 'JSON',\n  mime: 'application/json',\n  mimes: ['application/json', 'application/x-json'],\n  mode: 'javascript',\n  ext: ['json', 'map'],\n  alias: ['json5']\n}, {\n  name: 'Lua',\n  mime: 'text/x-lua',\n  mode: 'lua',\n  ext: ['lua']\n}, {\n  name: 'Markdown',\n  mime: 'text/x-markdown',\n  mode: 'markdown',\n  ext: ['markdown', 'md', 'mkd']\n}, {\n  name: 'MariaDB',\n  mime: 'text/x-mariadb',\n  mode: 'sql'\n}, {\n  name: 'MS SQL',\n  mime: 'text/x-mssql',\n  mode: 'sql'\n}, {\n  name: 'MySQL',\n  mime: 'text/x-mysql',\n  mode: 'sql'\n}, {\n  name: 'Nginx',\n  mime: 'text/x-nginx-conf',\n  mode: 'nginx',\n  file: /nginx.*\\.conf$/i\n}, {\n  name: 'PHP',\n  mime: 'text/x-php',\n  mimes: ['text/x-php', 'application/x-httpd-php', 'application/x-httpd-php-open'],\n  mode: 'php',\n  ext: ['php', 'php3', 'php4', 'php5', 'php7', 'phtml']\n}, {\n  name: 'Plain Text',\n  mime: 'text/plain',\n  mode: 'null',\n  ext: ['txt', 'text', 'conf', 'def', 'list', 'log']\n}, {\n  name: 'PostgreSQL',\n  mime: 'text/x-pgsql',\n  mode: 'sql'\n}, {\n  name: 'Properties',\n  mime: 'text/x-properties',\n  mode: 'properties',\n  ext: ['properties', 'ini', 'in'],\n  alias: ['ini', 'properties']\n}, {\n  name: 'Pug',\n  mime: 'text/x-pug',\n  mimes: ['text/x-pug', 'text/x-jade'],\n  mode: 'null',\n  ext: ['pug']\n}, {\n  name: 'Python',\n  mime: 'text/x-python',\n  mode: 'python',\n  ext: ['BUILD', 'bzl', 'py', 'pyw'],\n  file: /^(BUCK|BUILD)$/\n}, {\n  name: 'Ruby',\n  mime: 'text/x-ruby',\n  mode: 'ruby',\n  ext: ['rb'],\n  alias: ['jruby', 'macruby', 'rake', 'rb', 'rbx']\n}, {\n  name: 'Rust',\n  mime: 'text/x-rustsrc',\n  mode: 'rust',\n  ext: ['rs']\n}, {\n  name: 'Sass',\n  mime: 'text/x-sass',\n  mode: 'sass',\n  ext: ['sass']\n}, {\n  name: 'SCSS',\n  mime: 'text/x-scss',\n  mode: 'css',\n  ext: ['scss']\n}, {\n  name: 'Shell',\n  mime: 'text/x-sh',\n  mimes: ['text/x-sh', 'application/x-sh'],\n  mode: 'shell',\n  ext: ['sh', 'ksh', 'bash'],\n  alias: ['bash', 'sh', 'zsh'],\n  file: /^PKGBUILD$/\n}, {\n  name: 'SQL',\n  mime: 'text/x-sql',\n  mode: 'sql',\n  ext: ['sql']\n}, {\n  name: 'SQLite',\n  mime: 'text/x-sqlite',\n  mode: 'sql'\n}, {\n  name: 'TOML',\n  mime: 'text/x-toml',\n  mode: 'toml',\n  ext: ['toml']\n}, {\n  name: 'TypeScript',\n  mime: 'application/typescript',\n  mode: 'javascript',\n  ext: ['ts'],\n  alias: ['ts']\n}, {\n  name: 'Vue',\n  mime: 'script/x-vue',\n  mimes: ['script/x-vue', 'text/x-vue'],\n  mode: 'vue',\n  ext: ['vue']\n}, {\n  name: 'XML',\n  mime: 'application/xml',\n  mimes: ['application/xml', 'text/xml'],\n  mode: 'xml',\n  ext: ['xml', 'xsl', 'xsd', 'svg'],\n  alias: ['rss', 'wsdl', 'xsd']\n}, {\n  name: 'YAML',\n  mime: 'text/x-yaml',\n  mimes: ['text/x-yaml', 'text/yaml'],\n  mode: 'yaml',\n  ext: ['yaml', 'yml'],\n  alias: ['yml']\n}];\nconst _default = modes;\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(modes, \"modes\", \"/workspaces/Pterod/resources/scripts/modes.ts\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/modes.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/modes.ts\n");

/***/ })

}]);