(window["webpackJsonp"] = window["webpackJsonp"] || []).push([["auth~dashboard~server"],{

/***/ "./resources/scripts/components/elements/Field.tsx":
/*!*********************************************************!*\
  !*** ./resources/scripts/components/elements/Field.tsx ***!
  \*********************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! formik */ \"./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _components_elements_Input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/elements/Input */ \"./resources/scripts/components/elements/Input.tsx\");\n/* harmony import */ var _components_elements_Label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/elements/Label */ \"./resources/scripts/components/elements/Label.tsx\");\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\nconst Field = /*#__PURE__*/Object(react__WEBPACK_IMPORTED_MODULE_2__[\"forwardRef\"])((_ref, ref) => {\n  let {\n    id,\n    name,\n    light = false,\n    label,\n    description,\n    validate\n  } = _ref,\n      props = _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1___default()(_ref, [\"id\", \"name\", \"light\", \"label\", \"description\", \"validate\"]);\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(formik__WEBPACK_IMPORTED_MODULE_3__[/* Field */ \"a\"], {\n    innerRef: ref,\n    name: name,\n    validate: validate\n  }, _ref2 => {\n    let {\n      field,\n      form: {\n        errors,\n        touched\n      }\n    } = _ref2;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(\"div\", null, label && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_components_elements_Label__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"], {\n      htmlFor: id,\n      isLight: light\n    }, label), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_components_elements_Input__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"b\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n      id: id\n    }, field, props, {\n      isLight: light,\n      hasError: !!(touched[field.name] && errors[field.name])\n    })), touched[field.name] && errors[field.name] ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(\"p\", {\n      className: 'input-help error'\n    }, errors[field.name].charAt(0).toUpperCase() + errors[field.name].slice(1)) : description ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(\"p\", {\n      className: 'input-help'\n    }, description) : null);\n  });\n});\nField.displayName = 'Field';\nconst _default = Field;\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(Field, \"Field\", \"/workspaces/Pterod/resources/scripts/components/elements/Field.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/elements/Field.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/elements/Field.tsx\n");

/***/ }),

/***/ "./resources/scripts/components/elements/Input.tsx":
/*!*********************************************************!*\
  !*** ./resources/scripts/components/elements/Input.tsx ***!
  \*********************************************************/
/*! exports provided: Textarea, default */
/*! exports used: Textarea, default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"a\", function() { return Textarea; });\n/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/taggedTemplateLiteral */ \"./node_modules/@babel/runtime/helpers/taggedTemplateLiteral.js\");\n/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nfunction _templateObject3() {\n  const data = _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0___default()([\"\\n    // Reset to normal styling.\\n    resize: none;\\n    \", \";\\n    \", \";\\n    \", \";\\n\\n    & + .input-help {\\n        \", \";\\n        \", \";\\n    }\\n\\n    &:required,\\n    &:invalid {\\n        \", \";\\n    }\\n\\n    &:not(:disabled):not(:read-only):focus {\\n        \", \";\\n        \", \";\\n    }\\n\\n    &:disabled {\\n        \", \";\\n    }\\n\\n    \", \";\\n    \", \";\\n\"]);\n\n  _templateObject3 = function () {\n    return data;\n  };\n\n  return data;\n}\n\nfunction _templateObject2() {\n  const data = _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0___default()([\"\\n    \", \";\\n    color-adjust: exact;\\n    background-origin: border-box;\\n    transition: all 75ms linear, box-shadow 25ms linear;\\n\\n    &:checked {\\n        \", \";\\n        background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M5.707 7.293a1 1 0 0 0-1.414 1.414l2 2a1 1 0 0 0 1.414 0l4-4a1 1 0 0 0-1.414-1.414L7 8.586 5.707 7.293z'/%3e%3c/svg%3e\\\");\\n        background-color: currentColor;\\n        background-size: 100% 100%;\\n    }\\n\\n    &:focus {\\n        \", \";\\n        box-shadow: 0 0 0 1px rgba(9, 103, 210, 0.25);\\n    }\\n\"]);\n\n  _templateObject2 = function () {\n    return data;\n  };\n\n  return data;\n}\n\nfunction _templateObject() {\n  const data = _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_0___default()([\"\\n    \", \";\\n    &:focus {\\n        \", \"\\n    }\\n\\n    &:disabled {\\n        \", \";\\n    }\\n\"]);\n\n  _templateObject = function () {\n    return data;\n  };\n\n  return data;\n}\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\nconst light = Object(styled_components__WEBPACK_IMPORTED_MODULE_1__[/* css */ \"b\"])(_templateObject(), {\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"rgba(255, 255, 255, var(--tw-bg-opacity))\",\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"hsla(210, 16%, 82%, var(--tw-border-opacity))\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(209, 20%, 25%, var(--tw-text-opacity))\"\n}, {\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"rgba(96, 165, 250, var(--tw-border-opacity))\"\n}, {\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(214, 15%, 91%, var(--tw-bg-opacity))\",\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"hsla(210, 16%, 82%, var(--tw-border-opacity))\"\n});\n\nconst checkboxStyle = Object(styled_components__WEBPACK_IMPORTED_MODULE_1__[/* css */ \"b\"])(_templateObject2(), {\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(211, 12%, 43%, var(--tw-bg-opacity))\",\n  \"cursor\": \"pointer\",\n  \"appearance\": \"none\",\n  \"display\": \"inline-block\",\n  \"verticalAlign\": \"middle\",\n  \"userSelect\": \"none\",\n  \"flexShrink\": \"0\",\n  \"width\": \"1rem\",\n  \"height\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"rgba(96, 165, 250, var(--tw-text-opacity))\",\n  \"borderWidth\": \"1px\",\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"hsla(211, 13%, 65%, var(--tw-border-opacity))\",\n  \"borderRadius\": \"0.125rem\"\n}, {\n  \"borderColor\": \"rgba(0, 0, 0, 0)\",\n  \"backgroundRepeat\": \"no-repeat\",\n  \"backgroundPosition\": \"center\"\n}, {\n  \"outline\": \"2px solid transparent\",\n  \"outlineOffset\": \"2px\",\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"rgba(147, 197, 253, var(--tw-border-opacity))\"\n});\n\nconst inputStyle = Object(styled_components__WEBPACK_IMPORTED_MODULE_1__[/* css */ \"b\"])(_templateObject3(), {\n  \"appearance\": \"none\",\n  \"outline\": \"2px solid transparent\",\n  \"outlineOffset\": \"2px\",\n  \"width\": \"100%\",\n  \"minWidth\": \"0px\"\n}, {\n  \"padding\": \"0.75rem\",\n  \"borderWidth\": \"2px\",\n  \"borderRadius\": \"0.25rem\",\n  \"fontSize\": \"0.875rem\",\n  \"lineHeight\": \"1.25rem\",\n  \"transitionProperty\": \"all\",\n  \"transitionTimingFunction\": \"cubic-bezier(0.4, 0, 0.2, 1)\",\n  \"transitionDuration\": \"150ms\"\n}, {\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(209, 14%, 37%, var(--tw-bg-opacity))\",\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"hsla(211, 12%, 43%, var(--tw-border-opacity))\",\n  \":hover\": {\n    \"--tw-border-opacity\": \"1\",\n    \"borderColor\": \"hsla(211, 10%, 53%, var(--tw-border-opacity))\"\n  },\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(210, 16%, 82%, var(--tw-text-opacity))\",\n  \"--tw-shadow\": \"0 0 #0000\",\n  \"boxShadow\": \"var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)\",\n  \":focus\": {\n    \"--tw-ring-offset-shadow\": \"var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)\",\n    \"--tw-ring-shadow\": \"var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color)\",\n    \"boxShadow\": \"var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)\"\n  }\n}, {\n  \"marginTop\": \"0.25rem\",\n  \"fontSize\": \"0.75rem\",\n  \"lineHeight\": \"1rem\"\n}, props => props.hasError ? {\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"rgba(254, 202, 202, var(--tw-text-opacity))\"\n} : {\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(210, 16%, 82%, var(--tw-text-opacity))\"\n}, {\n  \"--tw-shadow\": \"0 0 #0000\",\n  \"boxShadow\": \"var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)\"\n}, {\n  \"--tw-shadow\": \"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)\",\n  \"boxShadow\": \"var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000)\",\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"rgba(147, 197, 253, var(--tw-border-opacity))\",\n  \"--tw-ring-offset-shadow\": \"var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color)\",\n  \"--tw-ring-shadow\": \"var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color)\",\n  \"--tw-ring-opacity\": \"0.5\",\n  \"--tw-ring-color\": \"rgba(96, 165, 250, var(--tw-ring-opacity))\"\n}, props => props.hasError && {\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"rgba(252, 165, 165, var(--tw-border-opacity))\",\n  \"--tw-ring-opacity\": \"1\",\n  \"--tw-ring-color\": \"rgba(254, 202, 202, var(--tw-ring-opacity))\"\n}, {\n  \"opacity\": \"0.75\"\n}, props => props.isLight && light, props => props.hasError && {\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"rgba(254, 226, 226, var(--tw-text-opacity))\",\n  \"--tw-border-opacity\": \"1\",\n  \"borderColor\": \"rgba(248, 113, 113, var(--tw-border-opacity))\",\n  \":hover\": {\n    \"--tw-border-opacity\": \"1\",\n    \"borderColor\": \"rgba(252, 165, 165, var(--tw-border-opacity))\"\n  }\n});\n\nconst Input = /*#__PURE__*/styled_components__WEBPACK_IMPORTED_MODULE_1__[/* default */ \"c\"].input.withConfig({\n  displayName: \"Input\",\n  componentId: \"sc-19rce1w-0\"\n})([\"&:not([type='checkbox']):not([type='radio']){\", \";}&[type='checkbox'],&[type='radio']{\", \";&[type='radio']{\", \";}}\"], inputStyle, checkboxStyle, {\n  \"borderRadius\": \"9999px\"\n});\n\nconst Textarea = /*#__PURE__*/styled_components__WEBPACK_IMPORTED_MODULE_1__[/* default */ \"c\"].textarea.withConfig({\n  displayName: \"Input__Textarea\",\n  componentId: \"sc-19rce1w-1\"\n})([\"\", \"\"], inputStyle);\n\n\nconst _default = Input;\n/* harmony default export */ __webpack_exports__[\"b\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(light, \"light\", \"/workspaces/Pterod/resources/scripts/components/elements/Input.tsx\");\n  reactHotLoader.register(checkboxStyle, \"checkboxStyle\", \"/workspaces/Pterod/resources/scripts/components/elements/Input.tsx\");\n  reactHotLoader.register(inputStyle, \"inputStyle\", \"/workspaces/Pterod/resources/scripts/components/elements/Input.tsx\");\n  reactHotLoader.register(Input, \"Input\", \"/workspaces/Pterod/resources/scripts/components/elements/Input.tsx\");\n  reactHotLoader.register(Textarea, \"Textarea\", \"/workspaces/Pterod/resources/scripts/components/elements/Input.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/elements/Input.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/elements/Input.tsx\n");

/***/ }),

/***/ "./resources/scripts/components/elements/Label.tsx":
/*!*********************************************************!*\
  !*** ./resources/scripts/components/elements/Label.tsx ***!
  \*********************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\nconst Label = /*#__PURE__*/styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"].label.withConfig({\n  displayName: \"Label\",\n  componentId: \"sc-g780ms-0\"\n})([\"\", \";\", \";\"], {\n  \"display\": \"block\",\n  \"fontSize\": \"0.75rem\",\n  \"lineHeight\": \"1rem\",\n  \"textTransform\": \"uppercase\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(210, 16%, 82%, var(--tw-text-opacity))\",\n  \"marginBottom\": \"0.25rem\",\n  \"@media (min-width: 640px)\": {\n    \"marginBottom\": \"0.5rem\"\n  }\n}, props => props.isLight && {\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(209, 18%, 30%, var(--tw-text-opacity))\"\n});\n\nconst _default = Label;\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(Label, \"Label\", \"/workspaces/Pterod/resources/scripts/components/elements/Label.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/elements/Label.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9jb21wb25lbnRzL2VsZW1lbnRzL0xhYmVsLnRzeD9iYTk0Il0sIm5hbWVzIjpbIkxhYmVsIiwic3R5bGVkIiwibGFiZWwiLCJwcm9wcyIsImlzTGlnaHQiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUdBLE1BQU1BLEtBQUssZ0JBQUdDLGlFQUFNLENBQUNDLEtBQVY7RUFBQTtFQUFBO0FBQUEsbUJBQ0g7RUFBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUE7RUFBQTtBQUFBLENBREcsRUFFSkMsS0FBRCxJQUFXQSxLQUFLLENBQUNDLE9BQU4sSUFBbUI7RUFBQTtFQUFBO0FBQUEsQ0FGekIsQ0FBWDs7aUJBS2VKLEs7QUFBQTs7Ozs7Ozs7OzswQkFMVEEsSyIsImZpbGUiOiIuL3Jlc291cmNlcy9zY3JpcHRzL2NvbXBvbmVudHMvZWxlbWVudHMvTGFiZWwudHN4LmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHN0eWxlZCBmcm9tICdzdHlsZWQtY29tcG9uZW50cy9tYWNybyc7XG5pbXBvcnQgdHcgZnJvbSAndHdpbi5tYWNybyc7XG5cbmNvbnN0IExhYmVsID0gc3R5bGVkLmxhYmVsPHsgaXNMaWdodD86IGJvb2xlYW4gfT5gXG4gICAgJHt0d2BibG9jayB0ZXh0LXhzIHVwcGVyY2FzZSB0ZXh0LW5ldXRyYWwtMjAwIG1iLTEgc206bWItMmB9O1xuICAgICR7KHByb3BzKSA9PiBwcm9wcy5pc0xpZ2h0ICYmIHR3YHRleHQtbmV1dHJhbC03MDBgfTtcbmA7XG5cbmV4cG9ydCBkZWZhdWx0IExhYmVsO1xuIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./resources/scripts/components/elements/Label.tsx\n");

/***/ }),

/***/ "./resources/scripts/plugins/useFlash.ts":
/*!***********************************************!*\
  !*** ./resources/scripts/plugins/useFlash.ts ***!
  \***********************************************/
/*! exports provided: useFlashKey, default */
/*! exports used: default, useFlashKey */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, \"b\", function() { return useFlashKey; });\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! easy-peasy */ \"./node_modules/easy-peasy/dist/easy-peasy.js\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(easy_peasy__WEBPACK_IMPORTED_MODULE_0__);\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\nconst useFlash = () => {\n  return Object(easy_peasy__WEBPACK_IMPORTED_MODULE_0__[\"useStoreActions\"])(actions => actions.flashes);\n};\n\n__signature__(useFlash, \"useStoreActions{}\", () => [easy_peasy__WEBPACK_IMPORTED_MODULE_0__[\"useStoreActions\"]]);\n\nconst useFlashKey = key => {\n  const {\n    addFlash,\n    clearFlashes,\n    clearAndAddHttpError\n  } = useFlash();\n  return {\n    addError: (message, title) => addFlash({\n      key,\n      message,\n      title,\n      type: 'error'\n    }),\n    clearFlashes: () => clearFlashes(key),\n    clearAndAddHttpError: error => clearAndAddHttpError({\n      key,\n      error\n    })\n  };\n};\n\n__signature__(useFlashKey, \"useFlash{{ addFlash, clearFlashes, clearAndAddHttpError }}\", () => [useFlash]);\n\n\nconst _default = useFlash;\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(useFlash, \"useFlash\", \"/workspaces/Pterod/resources/scripts/plugins/useFlash.ts\");\n  reactHotLoader.register(useFlashKey, \"useFlashKey\", \"/workspaces/Pterod/resources/scripts/plugins/useFlash.ts\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/plugins/useFlash.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/plugins/useFlash.ts\n");

/***/ })

}]);