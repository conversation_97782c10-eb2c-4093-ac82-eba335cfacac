(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[5],{

/***/ "./resources/scripts/components/server/features/GSLTokenModalFeature.tsx":
/*!*******************************************************************************!*\
  !*** ./resources/scripts/components/server/features/GSLTokenModalFeature.tsx ***!
  \*******************************************************************************/
/*! exports provided: default */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _state_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/state/server */ \"./resources/scripts/state/server/index.ts\");\n/* harmony import */ var _components_elements_Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/elements/Modal */ \"./resources/scripts/components/elements/Modal.tsx\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n/* harmony import */ var _components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/FlashMessageRender */ \"./resources/scripts/components/FlashMessageRender.tsx\");\n/* harmony import */ var _plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/plugins/useFlash */ \"./resources/scripts/plugins/useFlash.ts\");\n/* harmony import */ var _components_server_events__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/server/events */ \"./resources/scripts/components/server/events.ts\");\n/* harmony import */ var _components_elements_Field__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/elements/Field */ \"./resources/scripts/components/elements/Field.tsx\");\n/* harmony import */ var _api_server_updateStartupVariable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/api/server/updateStartupVariable */ \"./resources/scripts/api/server/updateStartupVariable.ts\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! formik */ \"./node_modules/formik/dist/formik.esm.js\");\n\n\n\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\n\n\nconst GSLTokenModalFeature = () => {\n  const [visible, setVisible] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const [loading, setLoading] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const uuid = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.server.data.uuid);\n  const status = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.status.value);\n  const {\n    clearFlashes,\n    clearAndAddHttpError\n  } = Object(_plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"])();\n  const {\n    connected,\n    instance\n  } = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.socket);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    if (!connected || !instance || status === 'running') return;\n    const errors = ['(gsl token expired)', '(account not found)'];\n\n    const listener = line => {\n      if (errors.some(p => line.toLowerCase().includes(p))) {\n        setVisible(true);\n      }\n    };\n\n    instance.addListener(_components_server_events__WEBPACK_IMPORTED_MODULE_7__[/* SocketEvent */ \"a\"].CONSOLE_OUTPUT, listener);\n    return () => {\n      instance.removeListener(_components_server_events__WEBPACK_IMPORTED_MODULE_7__[/* SocketEvent */ \"a\"].CONSOLE_OUTPUT, listener);\n    };\n  }, [connected, instance, status]);\n\n  const updateGSLToken = values => {\n    setLoading(true);\n    clearFlashes('feature:gslToken');\n    Object(_api_server_updateStartupVariable__WEBPACK_IMPORTED_MODULE_9__[/* default */ \"a\"])(uuid, 'STEAM_ACC', values.gslToken).then(() => {\n      if (instance) {\n        instance.send(_components_server_events__WEBPACK_IMPORTED_MODULE_7__[/* SocketRequest */ \"b\"].SET_STATE, 'restart');\n      }\n\n      setLoading(false);\n      setVisible(false);\n    }).catch(error => {\n      console.error(error);\n      clearAndAddHttpError({\n        key: 'feature:gslToken',\n        error\n      });\n    }).then(() => setLoading(false));\n  };\n\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    clearFlashes('feature:gslToken');\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(formik__WEBPACK_IMPORTED_MODULE_10__[/* Formik */ \"c\"], {\n    onSubmit: updateGSLToken,\n    initialValues: {\n      gslToken: ''\n    }\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Modal__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"b\"], {\n    visible: visible,\n    onDismissed: () => setVisible(false),\n    closeOnBackground: false,\n    showSpinnerOverlay: loading\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledFlashMessageRender, {\n    key: 'feature:gslToken',\n    \"data-tw\": \"mb-4\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(formik__WEBPACK_IMPORTED_MODULE_10__[/* Form */ \"b\"], null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledH, {\n    \"data-tw\": \"text-2xl mb-4 text-neutral-100\"\n  }, \"Invalid GSL token!\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP, {\n    \"data-tw\": \"mt-4\"\n  }, \"It seems like your Gameserver Login Token (GSL token) is invalid or has expired.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP2, {\n    \"data-tw\": \"mt-4\"\n  }, \"You can either generate a new one and enter it below or leave the field blank to remove it completely.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv, {\n    \"data-tw\": \"sm:flex items-center mt-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Field__WEBPACK_IMPORTED_MODULE_8__[/* default */ \"a\"], {\n    name: 'gslToken',\n    label: 'GSL Token',\n    description: 'Visit https://steamcommunity.com/dev/managegameservers to generate a token.',\n    autoFocus: true\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv2, {\n    \"data-tw\": \"mt-8 sm:flex items-center justify-end\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton, {\n    type: 'submit',\n    \"data-tw\": \"mt-4 sm:mt-0 sm:ml-4 w-full sm:w-auto\"\n  }, \"Update GSL Token\")))));\n};\n\n__signature__(GSLTokenModalFeature, \"useState{[visible, setVisible](false)}\\nuseState{[loading, setLoading](false)}\\nuseStoreState{uuid}\\nuseStoreState{status}\\nuseFlash{{ clearFlashes, clearAndAddHttpError }}\\nuseStoreState{{ connected, instance }}\\nuseEffect{}\\nuseEffect{}\", () => [_plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"]]);\n\nconst _default = GSLTokenModalFeature;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_default);\n\nvar _StyledFlashMessageRender = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"]).withConfig({\n  displayName: \"GSLTokenModalFeature___StyledFlashMessageRender\",\n  componentId: \"sc-1r409kx-0\"\n})({\n  \"marginBottom\": \"1rem\"\n});\n\nvar _StyledH = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"h2\").withConfig({\n  displayName: \"GSLTokenModalFeature___StyledH\",\n  componentId: \"sc-1r409kx-1\"\n})({\n  \"fontSize\": \"1.5rem\",\n  \"lineHeight\": \"2rem\",\n  \"marginBottom\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(214, 15%, 91%, var(--tw-text-opacity))\"\n});\n\nvar _StyledP = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"GSLTokenModalFeature___StyledP\",\n  componentId: \"sc-1r409kx-2\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledP2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"GSLTokenModalFeature___StyledP2\",\n  componentId: \"sc-1r409kx-3\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"GSLTokenModalFeature___StyledDiv\",\n  componentId: \"sc-1r409kx-4\"\n})({\n  \"alignItems\": \"center\",\n  \"marginTop\": \"1rem\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledDiv2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"GSLTokenModalFeature___StyledDiv2\",\n  componentId: \"sc-1r409kx-5\"\n})({\n  \"marginTop\": \"2rem\",\n  \"alignItems\": \"center\",\n  \"justifyContent\": \"flex-end\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledButton = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"]).withConfig({\n  displayName: \"GSLTokenModalFeature___StyledButton\",\n  componentId: \"sc-1r409kx-6\"\n})({\n  \"marginTop\": \"1rem\",\n  \"width\": \"100%\",\n  \"@media (min-width: 640px)\": {\n    \"marginTop\": \"0px\",\n    \"marginLeft\": \"1rem\",\n    \"width\": \"auto\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(GSLTokenModalFeature, \"GSLTokenModalFeature\", \"/workspaces/Pterod/resources/scripts/components/server/features/GSLTokenModalFeature.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/server/features/GSLTokenModalFeature.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9jb21wb25lbnRzL3NlcnZlci9mZWF0dXJlcy9HU0xUb2tlbk1vZGFsRmVhdHVyZS50c3g/ZWFmOCJdLCJuYW1lcyI6WyJHU0xUb2tlbk1vZGFsRmVhdHVyZSIsInZpc2libGUiLCJzZXRWaXNpYmxlIiwidXNlU3RhdGUiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInV1aWQiLCJTZXJ2ZXJDb250ZXh0IiwidXNlU3RvcmVTdGF0ZSIsInN0YXRlIiwic2VydmVyIiwiZGF0YSIsInN0YXR1cyIsInZhbHVlIiwiY2xlYXJGbGFzaGVzIiwiY2xlYXJBbmRBZGRIdHRwRXJyb3IiLCJ1c2VGbGFzaCIsImNvbm5lY3RlZCIsImluc3RhbmNlIiwic29ja2V0IiwidXNlRWZmZWN0IiwiZXJyb3JzIiwibGlzdGVuZXIiLCJsaW5lIiwic29tZSIsInAiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwiYWRkTGlzdGVuZXIiLCJTb2NrZXRFdmVudCIsIkNPTlNPTEVfT1VUUFVUIiwicmVtb3ZlTGlzdGVuZXIiLCJ1cGRhdGVHU0xUb2tlbiIsInZhbHVlcyIsInVwZGF0ZVN0YXJ0dXBWYXJpYWJsZSIsImdzbFRva2VuIiwidGhlbiIsInNlbmQiLCJTb2NrZXRSZXF1ZXN0IiwiU0VUX1NUQVRFIiwiY2F0Y2giLCJlcnJvciIsImNvbnNvbGUiLCJrZXkiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQU1BLE1BQU1BLG9CQUFvQixHQUFHLE1BQU07RUFDL0IsTUFBTSxDQUFDQyxPQUFELEVBQVVDLFVBQVYsSUFBd0JDLHNEQUFRLENBQUMsS0FBRCxDQUF0QztFQUNBLE1BQU0sQ0FBQ0MsT0FBRCxFQUFVQyxVQUFWLElBQXdCRixzREFBUSxDQUFDLEtBQUQsQ0FBdEM7RUFFQSxNQUFNRyxJQUFJLEdBQUdDLG1FQUFhLENBQUNDLGFBQWQsQ0FBNkJDLEtBQUQsSUFBV0EsS0FBSyxDQUFDQyxNQUFOLENBQWFDLElBQWIsQ0FBbUJMLElBQTFELENBQWI7RUFDQSxNQUFNTSxNQUFNLEdBQUdMLG1FQUFhLENBQUNDLGFBQWQsQ0FBNkJDLEtBQUQsSUFBV0EsS0FBSyxDQUFDRyxNQUFOLENBQWFDLEtBQXBELENBQWY7RUFDQSxNQUFNO0lBQUVDLFlBQUY7SUFBZ0JDO0VBQWhCLElBQXlDQyx5RUFBUSxFQUF2RDtFQUNBLE1BQU07SUFBRUMsU0FBRjtJQUFhQztFQUFiLElBQTBCWCxtRUFBYSxDQUFDQyxhQUFkLENBQTZCQyxLQUFELElBQVdBLEtBQUssQ0FBQ1UsTUFBN0MsQ0FBaEM7RUFFQUMsdURBQVMsQ0FBQyxNQUFNO0lBQ1osSUFBSSxDQUFDSCxTQUFELElBQWMsQ0FBQ0MsUUFBZixJQUEyQk4sTUFBTSxLQUFLLFNBQTFDLEVBQXFEO0lBRXJELE1BQU1TLE1BQU0sR0FBRyxDQUFDLHFCQUFELEVBQXdCLHFCQUF4QixDQUFmOztJQUVBLE1BQU1DLFFBQVEsR0FBSUMsSUFBRCxJQUFrQjtNQUMvQixJQUFJRixNQUFNLENBQUNHLElBQVAsQ0FBYUMsQ0FBRCxJQUFPRixJQUFJLENBQUNHLFdBQUwsR0FBbUJDLFFBQW5CLENBQTRCRixDQUE1QixDQUFuQixDQUFKLEVBQXdEO1FBQ3BEdkIsVUFBVSxDQUFDLElBQUQsQ0FBVjtNQUNIO0lBQ0osQ0FKRDs7SUFNQWdCLFFBQVEsQ0FBQ1UsV0FBVCxDQUFxQkMsNkVBQVcsQ0FBQ0MsY0FBakMsRUFBaURSLFFBQWpEO0lBRUEsT0FBTyxNQUFNO01BQ1RKLFFBQVEsQ0FBQ2EsY0FBVCxDQUF3QkYsNkVBQVcsQ0FBQ0MsY0FBcEMsRUFBb0RSLFFBQXBEO0lBQ0gsQ0FGRDtFQUdILENBaEJRLEVBZ0JOLENBQUNMLFNBQUQsRUFBWUMsUUFBWixFQUFzQk4sTUFBdEIsQ0FoQk0sQ0FBVDs7RUFrQkEsTUFBTW9CLGNBQWMsR0FBSUMsTUFBRCxJQUFvQjtJQUN2QzVCLFVBQVUsQ0FBQyxJQUFELENBQVY7SUFDQVMsWUFBWSxDQUFDLGtCQUFELENBQVo7SUFFQW9CLHlGQUFxQixDQUFDNUIsSUFBRCxFQUFPLFdBQVAsRUFBb0IyQixNQUFNLENBQUNFLFFBQTNCLENBQXJCLENBQ0tDLElBREwsQ0FDVSxNQUFNO01BQ1IsSUFBSWxCLFFBQUosRUFBYztRQUNWQSxRQUFRLENBQUNtQixJQUFULENBQWNDLCtFQUFhLENBQUNDLFNBQTVCLEVBQXVDLFNBQXZDO01BQ0g7O01BRURsQyxVQUFVLENBQUMsS0FBRCxDQUFWO01BQ0FILFVBQVUsQ0FBQyxLQUFELENBQVY7SUFDSCxDQVJMLEVBU0tzQyxLQVRMLENBU1lDLEtBQUQsSUFBVztNQUNkQyxPQUFPLENBQUNELEtBQVIsQ0FBY0EsS0FBZDtNQUNBMUIsb0JBQW9CLENBQUM7UUFBRTRCLEdBQUcsRUFBRSxrQkFBUDtRQUEyQkY7TUFBM0IsQ0FBRCxDQUFwQjtJQUNILENBWkwsRUFhS0wsSUFiTCxDQWFVLE1BQU0vQixVQUFVLENBQUMsS0FBRCxDQWIxQjtFQWNILENBbEJEOztFQW9CQWUsdURBQVMsQ0FBQyxNQUFNO0lBQ1pOLFlBQVksQ0FBQyxrQkFBRCxDQUFaO0VBQ0gsQ0FGUSxFQUVOLEVBRk0sQ0FBVDtFQUlBLG9CQUNJLDJEQUFDLHNEQUFEO0lBQVEsUUFBUSxFQUFFa0IsY0FBbEI7SUFBa0MsYUFBYSxFQUFFO01BQUVHLFFBQVEsRUFBRTtJQUFaO0VBQWpELGdCQUNJLDJEQUFDLDBFQUFEO0lBQ0ksT0FBTyxFQUFFbEMsT0FEYjtJQUVJLFdBQVcsRUFBRSxNQUFNQyxVQUFVLENBQUMsS0FBRCxDQUZqQztJQUdJLGlCQUFpQixFQUFFLEtBSHZCO0lBSUksa0JBQWtCLEVBQUVFO0VBSnhCLGdCQU1JO0lBQW9CLEdBQUcsRUFBRSxrQkFBekI7SUFBQTtFQUFBLEVBTkosZUFPSSwyREFBQyxvREFBRCxxQkFDSTtJQUFBO0VBQUEsd0JBREosZUFFSTtJQUFBO0VBQUEsc0ZBRkosZUFLSTtJQUFBO0VBQUEsNEdBTEosZUFTSTtJQUFBO0VBQUEsZ0JBQ0ksMkRBQUMsMEVBQUQ7SUFDSSxJQUFJLEVBQUUsVUFEVjtJQUVJLEtBQUssRUFBRSxXQUZYO0lBR0ksV0FBVyxFQUFFLDZFQUhqQjtJQUlJLFNBQVM7RUFKYixFQURKLENBVEosZUFpQkk7SUFBQTtFQUFBLGdCQUNJO0lBQVEsSUFBSSxFQUFFLFFBQWQ7SUFBQTtFQUFBLHNCQURKLENBakJKLENBUEosQ0FESixDQURKO0FBbUNILENBdEZEOztjQUFNSixvQiwyUEFNNkNnQixpRTs7aUJBa0ZwQ2hCLG9CO0FBQUE7Ozs7O0dBN0JxRDtFQUFBO0FBQUEsQzs7Ozs7R0FFckM7RUFBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FDRDtFQUFBO0FBQUEsQzs7Ozs7R0FHQTtFQUFBO0FBQUEsQzs7Ozs7R0FJRTtFQUFBO0VBQUE7RUFBQTtJQUFBO0VBQUE7QUFBQSxDOzs7OztHQVFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FDdUI7RUFBQTtFQUFBO0VBQUE7SUFBQTtJQUFBO0lBQUE7RUFBQTtBQUFBLEM7Ozs7Ozs7Ozs7OzBCQTlFakRBLG9CIiwiZmlsZSI6Ii4vcmVzb3VyY2VzL3NjcmlwdHMvY29tcG9uZW50cy9zZXJ2ZXIvZmVhdHVyZXMvR1NMVG9rZW5Nb2RhbEZlYXR1cmUudHN4LmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTZXJ2ZXJDb250ZXh0IH0gZnJvbSAnQC9zdGF0ZS9zZXJ2ZXInO1xuaW1wb3J0IE1vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9lbGVtZW50cy9Nb2RhbCc7XG5pbXBvcnQgdHcgZnJvbSAndHdpbi5tYWNybyc7XG5pbXBvcnQgQnV0dG9uIGZyb20gJ0AvY29tcG9uZW50cy9lbGVtZW50cy9CdXR0b24nO1xuaW1wb3J0IEZsYXNoTWVzc2FnZVJlbmRlciBmcm9tICdAL2NvbXBvbmVudHMvRmxhc2hNZXNzYWdlUmVuZGVyJztcbmltcG9ydCB1c2VGbGFzaCBmcm9tICdAL3BsdWdpbnMvdXNlRmxhc2gnO1xuaW1wb3J0IHsgU29ja2V0RXZlbnQsIFNvY2tldFJlcXVlc3QgfSBmcm9tICdAL2NvbXBvbmVudHMvc2VydmVyL2V2ZW50cyc7XG5pbXBvcnQgRmllbGQgZnJvbSAnQC9jb21wb25lbnRzL2VsZW1lbnRzL0ZpZWxkJztcbmltcG9ydCB1cGRhdGVTdGFydHVwVmFyaWFibGUgZnJvbSAnQC9hcGkvc2VydmVyL3VwZGF0ZVN0YXJ0dXBWYXJpYWJsZSc7XG5pbXBvcnQgeyBGb3JtLCBGb3JtaWsgfSBmcm9tICdmb3JtaWsnO1xuXG5pbnRlcmZhY2UgVmFsdWVzIHtcbiAgICBnc2xUb2tlbjogc3RyaW5nO1xufVxuXG5jb25zdCBHU0xUb2tlbk1vZGFsRmVhdHVyZSA9ICgpID0+IHtcbiAgICBjb25zdCBbdmlzaWJsZSwgc2V0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gICAgY29uc3QgdXVpZCA9IFNlcnZlckNvbnRleHQudXNlU3RvcmVTdGF0ZSgoc3RhdGUpID0+IHN0YXRlLnNlcnZlci5kYXRhIS51dWlkKTtcbiAgICBjb25zdCBzdGF0dXMgPSBTZXJ2ZXJDb250ZXh0LnVzZVN0b3JlU3RhdGUoKHN0YXRlKSA9PiBzdGF0ZS5zdGF0dXMudmFsdWUpO1xuICAgIGNvbnN0IHsgY2xlYXJGbGFzaGVzLCBjbGVhckFuZEFkZEh0dHBFcnJvciB9ID0gdXNlRmxhc2goKTtcbiAgICBjb25zdCB7IGNvbm5lY3RlZCwgaW5zdGFuY2UgfSA9IFNlcnZlckNvbnRleHQudXNlU3RvcmVTdGF0ZSgoc3RhdGUpID0+IHN0YXRlLnNvY2tldCk7XG5cbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoIWNvbm5lY3RlZCB8fCAhaW5zdGFuY2UgfHwgc3RhdHVzID09PSAncnVubmluZycpIHJldHVybjtcblxuICAgICAgICBjb25zdCBlcnJvcnMgPSBbJyhnc2wgdG9rZW4gZXhwaXJlZCknLCAnKGFjY291bnQgbm90IGZvdW5kKSddO1xuXG4gICAgICAgIGNvbnN0IGxpc3RlbmVyID0gKGxpbmU6IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgaWYgKGVycm9ycy5zb21lKChwKSA9PiBsaW5lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocCkpKSB7XG4gICAgICAgICAgICAgICAgc2V0VmlzaWJsZSh0cnVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcblxuICAgICAgICBpbnN0YW5jZS5hZGRMaXN0ZW5lcihTb2NrZXRFdmVudC5DT05TT0xFX09VVFBVVCwgbGlzdGVuZXIpO1xuXG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICBpbnN0YW5jZS5yZW1vdmVMaXN0ZW5lcihTb2NrZXRFdmVudC5DT05TT0xFX09VVFBVVCwgbGlzdGVuZXIpO1xuICAgICAgICB9O1xuICAgIH0sIFtjb25uZWN0ZWQsIGluc3RhbmNlLCBzdGF0dXNdKTtcblxuICAgIGNvbnN0IHVwZGF0ZUdTTFRva2VuID0gKHZhbHVlczogVmFsdWVzKSA9PiB7XG4gICAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICAgIGNsZWFyRmxhc2hlcygnZmVhdHVyZTpnc2xUb2tlbicpO1xuXG4gICAgICAgIHVwZGF0ZVN0YXJ0dXBWYXJpYWJsZSh1dWlkLCAnU1RFQU1fQUNDJywgdmFsdWVzLmdzbFRva2VuKVxuICAgICAgICAgICAgLnRoZW4oKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChpbnN0YW5jZSkge1xuICAgICAgICAgICAgICAgICAgICBpbnN0YW5jZS5zZW5kKFNvY2tldFJlcXVlc3QuU0VUX1NUQVRFLCAncmVzdGFydCcpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgICAgICAgICAgIHNldFZpc2libGUoZmFsc2UpO1xuICAgICAgICAgICAgfSlcbiAgICAgICAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKGVycm9yKTtcbiAgICAgICAgICAgICAgICBjbGVhckFuZEFkZEh0dHBFcnJvcih7IGtleTogJ2ZlYXR1cmU6Z3NsVG9rZW4nLCBlcnJvciB9KTtcbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAudGhlbigoKSA9PiBzZXRMb2FkaW5nKGZhbHNlKSk7XG4gICAgfTtcblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNsZWFyRmxhc2hlcygnZmVhdHVyZTpnc2xUb2tlbicpO1xuICAgIH0sIFtdKTtcblxuICAgIHJldHVybiAoXG4gICAgICAgIDxGb3JtaWsgb25TdWJtaXQ9e3VwZGF0ZUdTTFRva2VufSBpbml0aWFsVmFsdWVzPXt7IGdzbFRva2VuOiAnJyB9fT5cbiAgICAgICAgICAgIDxNb2RhbFxuICAgICAgICAgICAgICAgIHZpc2libGU9e3Zpc2libGV9XG4gICAgICAgICAgICAgICAgb25EaXNtaXNzZWQ9eygpID0+IHNldFZpc2libGUoZmFsc2UpfVxuICAgICAgICAgICAgICAgIGNsb3NlT25CYWNrZ3JvdW5kPXtmYWxzZX1cbiAgICAgICAgICAgICAgICBzaG93U3Bpbm5lck92ZXJsYXk9e2xvYWRpbmd9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPEZsYXNoTWVzc2FnZVJlbmRlciBrZXk9eydmZWF0dXJlOmdzbFRva2VuJ30gY3NzPXt0d2BtYi00YH0gLz5cbiAgICAgICAgICAgICAgICA8Rm9ybT5cbiAgICAgICAgICAgICAgICAgICAgPGgyIGNzcz17dHdgdGV4dC0yeGwgbWItNCB0ZXh0LW5ldXRyYWwtMTAwYH0+SW52YWxpZCBHU0wgdG9rZW4hPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY3NzPXt0d2BtdC00YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICBJdCBzZWVtcyBsaWtlIHlvdXIgR2FtZXNlcnZlciBMb2dpbiBUb2tlbiAoR1NMIHRva2VuKSBpcyBpbnZhbGlkIG9yIGhhcyBleHBpcmVkLlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNzcz17dHdgbXQtNGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgWW91IGNhbiBlaXRoZXIgZ2VuZXJhdGUgYSBuZXcgb25lIGFuZCBlbnRlciBpdCBiZWxvdyBvciBsZWF2ZSB0aGUgZmllbGQgYmxhbmsgdG8gcmVtb3ZlIGl0XG4gICAgICAgICAgICAgICAgICAgICAgICBjb21wbGV0ZWx5LlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY3NzPXt0d2BzbTpmbGV4IGl0ZW1zLWNlbnRlciBtdC00YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RmllbGRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lPXsnZ3NsVG9rZW4nfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPXsnR1NMIFRva2VuJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbj17J1Zpc2l0IGh0dHBzOi8vc3RlYW1jb21tdW5pdHkuY29tL2Rldi9tYW5hZ2VnYW1lc2VydmVycyB0byBnZW5lcmF0ZSBhIHRva2VuLid9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXV0b0ZvY3VzXG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjc3M9e3R3YG10LTggc206ZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1lbmRgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdHlwZT17J3N1Ym1pdCd9IGNzcz17dHdgbXQtNCBzbTptdC0wIHNtOm1sLTQgdy1mdWxsIHNtOnctYXV0b2B9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFVwZGF0ZSBHU0wgVG9rZW5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0Zvcm0+XG4gICAgICAgICAgICA8L01vZGFsPlxuICAgICAgICA8L0Zvcm1paz5cbiAgICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgR1NMVG9rZW5Nb2RhbEZlYXR1cmU7XG4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./resources/scripts/components/server/features/GSLTokenModalFeature.tsx\n");

/***/ })

}]);