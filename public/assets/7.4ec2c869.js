(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[7],{

/***/ "./resources/scripts/components/server/features/PIDLimitModalFeature.tsx":
/*!*******************************************************************************!*\
  !*** ./resources/scripts/components/server/features/PIDLimitModalFeature.tsx ***!
  \*******************************************************************************/
/*! exports provided: default */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _state_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/state/server */ \"./resources/scripts/state/server/index.ts\");\n/* harmony import */ var _components_elements_Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/elements/Modal */ \"./resources/scripts/components/elements/Modal.tsx\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n/* harmony import */ var _components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/FlashMessageRender */ \"./resources/scripts/components/FlashMessageRender.tsx\");\n/* harmony import */ var _plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/plugins/useFlash */ \"./resources/scripts/plugins/useFlash.ts\");\n/* harmony import */ var _components_server_events__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/server/events */ \"./resources/scripts/components/server/events.ts\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! easy-peasy */ \"./node_modules/easy-peasy/dist/easy-peasy.js\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(easy_peasy__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @fortawesome/react-fontawesome */ \"./node_modules/@fortawesome/react-fontawesome/index.es.js\");\n/* harmony import */ var _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @fortawesome/free-solid-svg-icons */ \"./node_modules/@fortawesome/free-solid-svg-icons/index.es.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\n\n\nconst PIDLimitModalFeature = () => {\n  const [visible, setVisible] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const [loading] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const status = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.status.value);\n  const {\n    clearFlashes\n  } = Object(_plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"])();\n  const {\n    connected,\n    instance\n  } = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.socket);\n  const isAdmin = Object(easy_peasy__WEBPACK_IMPORTED_MODULE_8__[\"useStoreState\"])(state => state.user.data.rootAdmin);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    if (!connected || !instance || status === 'running') return;\n    const errors = ['pthread_create failed', 'failed to create thread', 'unable to create thread', 'unable to create native thread', 'unable to create new native thread', 'exception in thread \"craft async scheduler management thread\"'];\n\n    const listener = line => {\n      if (errors.some(p => line.toLowerCase().includes(p))) {\n        setVisible(true);\n      }\n    };\n\n    instance.addListener(_components_server_events__WEBPACK_IMPORTED_MODULE_7__[/* SocketEvent */ \"a\"].CONSOLE_OUTPUT, listener);\n    return () => {\n      instance.removeListener(_components_server_events__WEBPACK_IMPORTED_MODULE_7__[/* SocketEvent */ \"a\"].CONSOLE_OUTPUT, listener);\n    };\n  }, [connected, instance, status]);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    clearFlashes('feature:pidLimit');\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Modal__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"b\"], {\n    visible: visible,\n    onDismissed: () => setVisible(false),\n    closeOnBackground: false,\n    showSpinnerOverlay: loading\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledFlashMessageRender, {\n    key: 'feature:pidLimit',\n    \"data-tw\": \"mb-4\"\n  }), isAdmin ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(react__WEBPACK_IMPORTED_MODULE_1___default.a.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv, {\n    \"data-tw\": \"mt-4 sm:flex items-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledFontAwesomeIcon, {\n    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_10__[/* faExclamationTriangle */ \"w\"],\n    color: 'orange',\n    size: '4x',\n    \"data-tw\": \"pr-4\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledH, {\n    \"data-tw\": \"text-2xl mb-4 text-neutral-100\"\n  }, \"Memory or process limit reached...\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP, {\n    \"data-tw\": \"mt-4\"\n  }, \"This server has reached the maximum process or memory limit.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP2, {\n    \"data-tw\": \"mt-4\"\n  }, \"Increasing \", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledCode, {\n    \"data-tw\": \"font-mono bg-neutral-900\"\n  }, \"container_pid_limit\"), \" in the wings configuration, \", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledCode2, {\n    \"data-tw\": \"font-mono bg-neutral-900\"\n  }, \"config.yml\"), \", might help resolve this issue.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP3, {\n    \"data-tw\": \"mt-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(\"b\", null, \"Note: Wings must be restarted for the configuration file changes to take effect\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv2, {\n    \"data-tw\": \"mt-8 sm:flex items-center justify-end\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton, {\n    onClick: () => setVisible(false),\n    \"data-tw\": \"w-full sm:w-auto border-transparent\"\n  }, \"Close\"))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(react__WEBPACK_IMPORTED_MODULE_1___default.a.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv3, {\n    \"data-tw\": \"mt-4 sm:flex items-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledFontAwesomeIcon2, {\n    icon: _fortawesome_free_solid_svg_icons__WEBPACK_IMPORTED_MODULE_10__[/* faExclamationTriangle */ \"w\"],\n    color: 'orange',\n    size: '4x',\n    \"data-tw\": \"pr-4\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledH2, {\n    \"data-tw\": \"text-2xl mb-4 text-neutral-100\"\n  }, \"Possible resource limit reached...\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP4, {\n    \"data-tw\": \"mt-4\"\n  }, \"This server is attempting to use more resources than allocated. Please contact the administrator and give them the error below.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP5, {\n    \"data-tw\": \"mt-4\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledCode3, {\n    \"data-tw\": \"font-mono bg-neutral-900\"\n  }, \"pthread_create failed, Possibly out of memory or process/resource limits reached\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv4, {\n    \"data-tw\": \"mt-8 sm:flex items-center justify-end\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton2, {\n    onClick: () => setVisible(false),\n    \"data-tw\": \"w-full sm:w-auto border-transparent\"\n  }, \"Close\"))));\n};\n\n__signature__(PIDLimitModalFeature, \"useState{[visible, setVisible](false)}\\nuseState{[loading](false)}\\nuseStoreState{status}\\nuseFlash{{ clearFlashes }}\\nuseStoreState{{ connected, instance }}\\nuseStoreState{isAdmin}\\nuseEffect{}\\nuseEffect{}\", () => [_state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState, _plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"], _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState, easy_peasy__WEBPACK_IMPORTED_MODULE_8__[\"useStoreState\"]]);\n\nconst _default = PIDLimitModalFeature;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_default);\n\nvar _StyledFlashMessageRender = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"]).withConfig({\n  displayName: \"PIDLimitModalFeature___StyledFlashMessageRender\",\n  componentId: \"sc-13l6tmn-0\"\n})({\n  \"marginBottom\": \"1rem\"\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledDiv\",\n  componentId: \"sc-13l6tmn-1\"\n})({\n  \"marginTop\": \"1rem\",\n  \"alignItems\": \"center\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledFontAwesomeIcon = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__[/* FontAwesomeIcon */ \"a\"]).withConfig({\n  displayName: \"PIDLimitModalFeature___StyledFontAwesomeIcon\",\n  componentId: \"sc-13l6tmn-2\"\n})({\n  \"paddingRight\": \"1rem\"\n});\n\nvar _StyledH = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"h2\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledH\",\n  componentId: \"sc-13l6tmn-3\"\n})({\n  \"fontSize\": \"1.5rem\",\n  \"lineHeight\": \"2rem\",\n  \"marginBottom\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(214, 15%, 91%, var(--tw-text-opacity))\"\n});\n\nvar _StyledP = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledP\",\n  componentId: \"sc-13l6tmn-4\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledP2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledP2\",\n  componentId: \"sc-13l6tmn-5\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledCode = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"code\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledCode\",\n  componentId: \"sc-13l6tmn-6\"\n})({\n  \"fontFamily\": \"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(210, 24%, 16%, var(--tw-bg-opacity))\"\n});\n\nvar _StyledCode2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"code\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledCode2\",\n  componentId: \"sc-13l6tmn-7\"\n})({\n  \"fontFamily\": \"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(210, 24%, 16%, var(--tw-bg-opacity))\"\n});\n\nvar _StyledP3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledP3\",\n  componentId: \"sc-13l6tmn-8\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledDiv2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledDiv2\",\n  componentId: \"sc-13l6tmn-9\"\n})({\n  \"marginTop\": \"2rem\",\n  \"alignItems\": \"center\",\n  \"justifyContent\": \"flex-end\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledButton = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"]).withConfig({\n  displayName: \"PIDLimitModalFeature___StyledButton\",\n  componentId: \"sc-13l6tmn-10\"\n})({\n  \"width\": \"100%\",\n  \"borderColor\": \"rgba(0, 0, 0, 0)\",\n  \"@media (min-width: 640px)\": {\n    \"width\": \"auto\"\n  }\n});\n\nvar _StyledDiv3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledDiv3\",\n  componentId: \"sc-13l6tmn-11\"\n})({\n  \"marginTop\": \"1rem\",\n  \"alignItems\": \"center\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledFontAwesomeIcon2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_fortawesome_react_fontawesome__WEBPACK_IMPORTED_MODULE_9__[/* FontAwesomeIcon */ \"a\"]).withConfig({\n  displayName: \"PIDLimitModalFeature___StyledFontAwesomeIcon2\",\n  componentId: \"sc-13l6tmn-12\"\n})({\n  \"paddingRight\": \"1rem\"\n});\n\nvar _StyledH2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"h2\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledH2\",\n  componentId: \"sc-13l6tmn-13\"\n})({\n  \"fontSize\": \"1.5rem\",\n  \"lineHeight\": \"2rem\",\n  \"marginBottom\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(214, 15%, 91%, var(--tw-text-opacity))\"\n});\n\nvar _StyledP4 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledP4\",\n  componentId: \"sc-13l6tmn-14\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledP5 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledP5\",\n  componentId: \"sc-13l6tmn-15\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledCode3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"code\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledCode3\",\n  componentId: \"sc-13l6tmn-16\"\n})({\n  \"fontFamily\": \"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(210, 24%, 16%, var(--tw-bg-opacity))\"\n});\n\nvar _StyledDiv4 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"PIDLimitModalFeature___StyledDiv4\",\n  componentId: \"sc-13l6tmn-17\"\n})({\n  \"marginTop\": \"2rem\",\n  \"alignItems\": \"center\",\n  \"justifyContent\": \"flex-end\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledButton2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"]).withConfig({\n  displayName: \"PIDLimitModalFeature___StyledButton2\",\n  componentId: \"sc-13l6tmn-18\"\n})({\n  \"width\": \"100%\",\n  \"borderColor\": \"rgba(0, 0, 0, 0)\",\n  \"@media (min-width: 640px)\": {\n    \"width\": \"auto\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(PIDLimitModalFeature, \"PIDLimitModalFeature\", \"/workspaces/Pterod/resources/scripts/components/server/features/PIDLimitModalFeature.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/server/features/PIDLimitModalFeature.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9jb21wb25lbnRzL3NlcnZlci9mZWF0dXJlcy9QSURMaW1pdE1vZGFsRmVhdHVyZS50c3g/NTVlNSJdLCJuYW1lcyI6WyJQSURMaW1pdE1vZGFsRmVhdHVyZSIsInZpc2libGUiLCJzZXRWaXNpYmxlIiwidXNlU3RhdGUiLCJsb2FkaW5nIiwic3RhdHVzIiwiU2VydmVyQ29udGV4dCIsInVzZVN0b3JlU3RhdGUiLCJzdGF0ZSIsInZhbHVlIiwiY2xlYXJGbGFzaGVzIiwidXNlRmxhc2giLCJjb25uZWN0ZWQiLCJpbnN0YW5jZSIsInNvY2tldCIsImlzQWRtaW4iLCJ1c2VyIiwiZGF0YSIsInJvb3RBZG1pbiIsInVzZUVmZmVjdCIsImVycm9ycyIsImxpc3RlbmVyIiwibGluZSIsInNvbWUiLCJwIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImFkZExpc3RlbmVyIiwiU29ja2V0RXZlbnQiLCJDT05TT0xFX09VVFBVVCIsInJlbW92ZUxpc3RlbmVyIiwiZmFFeGNsYW1hdGlvblRyaWFuZ2xlIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsTUFBTUEsb0JBQW9CLEdBQUcsTUFBTTtFQUMvQixNQUFNLENBQUNDLE9BQUQsRUFBVUMsVUFBVixJQUF3QkMsc0RBQVEsQ0FBQyxLQUFELENBQXRDO0VBQ0EsTUFBTSxDQUFDQyxPQUFELElBQVlELHNEQUFRLENBQUMsS0FBRCxDQUExQjtFQUVBLE1BQU1FLE1BQU0sR0FBR0MsbUVBQWEsQ0FBQ0MsYUFBZCxDQUE2QkMsS0FBRCxJQUFXQSxLQUFLLENBQUNILE1BQU4sQ0FBYUksS0FBcEQsQ0FBZjtFQUNBLE1BQU07SUFBRUM7RUFBRixJQUFtQkMseUVBQVEsRUFBakM7RUFDQSxNQUFNO0lBQUVDLFNBQUY7SUFBYUM7RUFBYixJQUEwQlAsbUVBQWEsQ0FBQ0MsYUFBZCxDQUE2QkMsS0FBRCxJQUFXQSxLQUFLLENBQUNNLE1BQTdDLENBQWhDO0VBQ0EsTUFBTUMsT0FBTyxHQUFHUixnRUFBYSxDQUFFQyxLQUFELElBQVdBLEtBQUssQ0FBQ1EsSUFBTixDQUFXQyxJQUFYLENBQWlCQyxTQUE3QixDQUE3QjtFQUVBQyx1REFBUyxDQUFDLE1BQU07SUFDWixJQUFJLENBQUNQLFNBQUQsSUFBYyxDQUFDQyxRQUFmLElBQTJCUixNQUFNLEtBQUssU0FBMUMsRUFBcUQ7SUFFckQsTUFBTWUsTUFBTSxHQUFHLENBQ1gsdUJBRFcsRUFFWCx5QkFGVyxFQUdYLHlCQUhXLEVBSVgsZ0NBSlcsRUFLWCxvQ0FMVyxFQU1YLCtEQU5XLENBQWY7O0lBU0EsTUFBTUMsUUFBUSxHQUFJQyxJQUFELElBQWtCO01BQy9CLElBQUlGLE1BQU0sQ0FBQ0csSUFBUCxDQUFhQyxDQUFELElBQU9GLElBQUksQ0FBQ0csV0FBTCxHQUFtQkMsUUFBbkIsQ0FBNEJGLENBQTVCLENBQW5CLENBQUosRUFBd0Q7UUFDcER0QixVQUFVLENBQUMsSUFBRCxDQUFWO01BQ0g7SUFDSixDQUpEOztJQU1BVyxRQUFRLENBQUNjLFdBQVQsQ0FBcUJDLDZFQUFXLENBQUNDLGNBQWpDLEVBQWlEUixRQUFqRDtJQUVBLE9BQU8sTUFBTTtNQUNUUixRQUFRLENBQUNpQixjQUFULENBQXdCRiw2RUFBVyxDQUFDQyxjQUFwQyxFQUFvRFIsUUFBcEQ7SUFDSCxDQUZEO0VBR0gsQ0F2QlEsRUF1Qk4sQ0FBQ1QsU0FBRCxFQUFZQyxRQUFaLEVBQXNCUixNQUF0QixDQXZCTSxDQUFUO0VBeUJBYyx1REFBUyxDQUFDLE1BQU07SUFDWlQsWUFBWSxDQUFDLGtCQUFELENBQVo7RUFDSCxDQUZRLEVBRU4sRUFGTSxDQUFUO0VBSUEsb0JBQ0ksMkRBQUMsMEVBQUQ7SUFDSSxPQUFPLEVBQUVULE9BRGI7SUFFSSxXQUFXLEVBQUUsTUFBTUMsVUFBVSxDQUFDLEtBQUQsQ0FGakM7SUFHSSxpQkFBaUIsRUFBRSxLQUh2QjtJQUlJLGtCQUFrQixFQUFFRTtFQUp4QixnQkFNSTtJQUFvQixHQUFHLEVBQUUsa0JBQXpCO0lBQUE7RUFBQSxFQU5KLEVBT0tXLE9BQU8sZ0JBQ0oscUlBQ0k7SUFBQTtFQUFBLGdCQUNJO0lBQWdDLElBQUksRUFBRWdCLGdHQUF0QztJQUE2RCxLQUFLLEVBQUUsUUFBcEU7SUFBOEUsSUFBSSxFQUFFLElBQXBGO0lBQUE7RUFBQSxFQURKLGVBRUk7SUFBQTtFQUFBLHdDQUZKLENBREosZUFLSTtJQUFBO0VBQUEsa0VBTEosZUFNSTtJQUFBO0VBQUEsK0JBQ2U7SUFBQTtFQUFBLHlCQURmLGdEQUVtQjtJQUFBO0VBQUEsZ0JBRm5CLHFDQU5KLGVBV0k7SUFBQTtFQUFBLGdCQUNJLHdKQURKLENBWEosZUFjSTtJQUFBO0VBQUEsZ0JBQ0k7SUFBUSxPQUFPLEVBQUUsTUFBTTdCLFVBQVUsQ0FBQyxLQUFELENBQWpDO0lBQUE7RUFBQSxXQURKLENBZEosQ0FESSxnQkFzQkoscUlBQ0k7SUFBQTtFQUFBLGdCQUNJO0lBQWdDLElBQUksRUFBRTZCLGdHQUF0QztJQUE2RCxLQUFLLEVBQUUsUUFBcEU7SUFBOEUsSUFBSSxFQUFFLElBQXBGO0lBQUE7RUFBQSxFQURKLGVBRUk7SUFBQTtFQUFBLHdDQUZKLENBREosZUFLSTtJQUFBO0VBQUEscUlBTEosZUFTSTtJQUFBO0VBQUEsZ0JBQ0k7SUFBQTtFQUFBLHNGQURKLENBVEosZUFjSTtJQUFBO0VBQUEsZ0JBQ0k7SUFBUSxPQUFPLEVBQUUsTUFBTTdCLFVBQVUsQ0FBQyxLQUFELENBQWpDO0lBQUE7RUFBQSxXQURKLENBZEosQ0E3QlIsQ0FESjtBQXFESCxDQTNGRDs7Y0FBTUYsb0IsNE5BSWFNLG1FQUFhLENBQUNDLGEsRUFDSkksaUUsRUFDT0wsbUVBQWEsQ0FBQ0MsYSxFQUM5QkEsd0Q7O2lCQXNGTFAsb0I7QUFBQTs7Ozs7R0FoRGlEO0VBQUE7QUFBQSxDOzs7OztHQUdoQztFQUFBO0VBQUE7RUFBQTtJQUFBO0VBQUE7QUFBQSxDOzs7OztHQUNnQjtFQUFBO0FBQUEsQzs7Ozs7R0FDYjtFQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7QUFBQSxDOzs7OztHQUVMO0VBQUE7QUFBQSxDOzs7OztHQUNBO0VBQUE7QUFBQSxDOzs7OztHQUNrQjtFQUFBO0VBQUE7RUFBQTtBQUFBLEM7Ozs7O0dBQ0k7RUFBQTtFQUFBO0VBQUE7QUFBQSxDOzs7OztHQUd0QjtFQUFBO0FBQUEsQzs7Ozs7R0FHRTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUE7RUFBQTtBQUFBLEM7Ozs7O0dBQ3lDO0VBQUE7RUFBQTtFQUFBO0lBQUE7RUFBQTtBQUFBLEM7Ozs7O0dBT3pDO0VBQUE7RUFBQTtFQUFBO0lBQUE7RUFBQTtBQUFBLEM7Ozs7O0dBQ2dCO0VBQUE7QUFBQSxDOzs7OztHQUNiO0VBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtBQUFBLEM7Ozs7O0dBRUw7RUFBQTtBQUFBLEM7Ozs7O0dBSUE7RUFBQTtBQUFBLEM7Ozs7O0dBQ087RUFBQTtFQUFBO0VBQUE7QUFBQSxDOzs7OztHQUlMO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FDeUM7RUFBQTtFQUFBO0VBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7Ozs7Ozs7MEJBbkZuRUEsb0IiLCJmaWxlIjoiLi9yZXNvdXJjZXMvc2NyaXB0cy9jb21wb25lbnRzL3NlcnZlci9mZWF0dXJlcy9QSURMaW1pdE1vZGFsRmVhdHVyZS50c3guanMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFNlcnZlckNvbnRleHQgfSBmcm9tICdAL3N0YXRlL3NlcnZlcic7XG5pbXBvcnQgTW9kYWwgZnJvbSAnQC9jb21wb25lbnRzL2VsZW1lbnRzL01vZGFsJztcbmltcG9ydCB0dyBmcm9tICd0d2luLm1hY3JvJztcbmltcG9ydCBCdXR0b24gZnJvbSAnQC9jb21wb25lbnRzL2VsZW1lbnRzL0J1dHRvbic7XG5pbXBvcnQgRmxhc2hNZXNzYWdlUmVuZGVyIGZyb20gJ0AvY29tcG9uZW50cy9GbGFzaE1lc3NhZ2VSZW5kZXInO1xuaW1wb3J0IHVzZUZsYXNoIGZyb20gJ0AvcGx1Z2lucy91c2VGbGFzaCc7XG5pbXBvcnQgeyBTb2NrZXRFdmVudCB9IGZyb20gJ0AvY29tcG9uZW50cy9zZXJ2ZXIvZXZlbnRzJztcbmltcG9ydCB7IHVzZVN0b3JlU3RhdGUgfSBmcm9tICdlYXN5LXBlYXN5JztcbmltcG9ydCB7IEZvbnRBd2Vzb21lSWNvbiB9IGZyb20gJ0Bmb3J0YXdlc29tZS9yZWFjdC1mb250YXdlc29tZSc7XG5pbXBvcnQgeyBmYUV4Y2xhbWF0aW9uVHJpYW5nbGUgfSBmcm9tICdAZm9ydGF3ZXNvbWUvZnJlZS1zb2xpZC1zdmctaWNvbnMnO1xuXG5jb25zdCBQSURMaW1pdE1vZGFsRmVhdHVyZSA9ICgpID0+IHtcbiAgICBjb25zdCBbdmlzaWJsZSwgc2V0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW2xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gICAgY29uc3Qgc3RhdHVzID0gU2VydmVyQ29udGV4dC51c2VTdG9yZVN0YXRlKChzdGF0ZSkgPT4gc3RhdGUuc3RhdHVzLnZhbHVlKTtcbiAgICBjb25zdCB7IGNsZWFyRmxhc2hlcyB9ID0gdXNlRmxhc2goKTtcbiAgICBjb25zdCB7IGNvbm5lY3RlZCwgaW5zdGFuY2UgfSA9IFNlcnZlckNvbnRleHQudXNlU3RvcmVTdGF0ZSgoc3RhdGUpID0+IHN0YXRlLnNvY2tldCk7XG4gICAgY29uc3QgaXNBZG1pbiA9IHVzZVN0b3JlU3RhdGUoKHN0YXRlKSA9PiBzdGF0ZS51c2VyLmRhdGEhLnJvb3RBZG1pbik7XG5cbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoIWNvbm5lY3RlZCB8fCAhaW5zdGFuY2UgfHwgc3RhdHVzID09PSAncnVubmluZycpIHJldHVybjtcblxuICAgICAgICBjb25zdCBlcnJvcnMgPSBbXG4gICAgICAgICAgICAncHRocmVhZF9jcmVhdGUgZmFpbGVkJyxcbiAgICAgICAgICAgICdmYWlsZWQgdG8gY3JlYXRlIHRocmVhZCcsXG4gICAgICAgICAgICAndW5hYmxlIHRvIGNyZWF0ZSB0aHJlYWQnLFxuICAgICAgICAgICAgJ3VuYWJsZSB0byBjcmVhdGUgbmF0aXZlIHRocmVhZCcsXG4gICAgICAgICAgICAndW5hYmxlIHRvIGNyZWF0ZSBuZXcgbmF0aXZlIHRocmVhZCcsXG4gICAgICAgICAgICAnZXhjZXB0aW9uIGluIHRocmVhZCBcImNyYWZ0IGFzeW5jIHNjaGVkdWxlciBtYW5hZ2VtZW50IHRocmVhZFwiJyxcbiAgICAgICAgXTtcblxuICAgICAgICBjb25zdCBsaXN0ZW5lciA9IChsaW5lOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgIGlmIChlcnJvcnMuc29tZSgocCkgPT4gbGluZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHApKSkge1xuICAgICAgICAgICAgICAgIHNldFZpc2libGUodHJ1ZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG5cbiAgICAgICAgaW5zdGFuY2UuYWRkTGlzdGVuZXIoU29ja2V0RXZlbnQuQ09OU09MRV9PVVRQVVQsIGxpc3RlbmVyKTtcblxuICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgaW5zdGFuY2UucmVtb3ZlTGlzdGVuZXIoU29ja2V0RXZlbnQuQ09OU09MRV9PVVRQVVQsIGxpc3RlbmVyKTtcbiAgICAgICAgfTtcbiAgICB9LCBbY29ubmVjdGVkLCBpbnN0YW5jZSwgc3RhdHVzXSk7XG5cbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBjbGVhckZsYXNoZXMoJ2ZlYXR1cmU6cGlkTGltaXQnKTtcbiAgICB9LCBbXSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgICA8TW9kYWxcbiAgICAgICAgICAgIHZpc2libGU9e3Zpc2libGV9XG4gICAgICAgICAgICBvbkRpc21pc3NlZD17KCkgPT4gc2V0VmlzaWJsZShmYWxzZSl9XG4gICAgICAgICAgICBjbG9zZU9uQmFja2dyb3VuZD17ZmFsc2V9XG4gICAgICAgICAgICBzaG93U3Bpbm5lck92ZXJsYXk9e2xvYWRpbmd9XG4gICAgICAgID5cbiAgICAgICAgICAgIDxGbGFzaE1lc3NhZ2VSZW5kZXIga2V5PXsnZmVhdHVyZTpwaWRMaW1pdCd9IGNzcz17dHdgbWItNGB9IC8+XG4gICAgICAgICAgICB7aXNBZG1pbiA/IChcbiAgICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNzcz17dHdgbXQtNCBzbTpmbGV4IGl0ZW1zLWNlbnRlcmB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZvbnRBd2Vzb21lSWNvbiBjc3M9e3R3YHByLTRgfSBpY29uPXtmYUV4Y2xhbWF0aW9uVHJpYW5nbGV9IGNvbG9yPXsnb3JhbmdlJ30gc2l6ZT17JzR4J30gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMiBjc3M9e3R3YHRleHQtMnhsIG1iLTQgdGV4dC1uZXV0cmFsLTEwMCBgfT5NZW1vcnkgb3IgcHJvY2VzcyBsaW1pdCByZWFjaGVkLi4uPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNzcz17dHdgbXQtNGB9PlRoaXMgc2VydmVyIGhhcyByZWFjaGVkIHRoZSBtYXhpbXVtIHByb2Nlc3Mgb3IgbWVtb3J5IGxpbWl0LjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY3NzPXt0d2BtdC00YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICBJbmNyZWFzaW5nIDxjb2RlIGNzcz17dHdgZm9udC1tb25vIGJnLW5ldXRyYWwtOTAwYH0+Y29udGFpbmVyX3BpZF9saW1pdDwvY29kZT4gaW4gdGhlIHdpbmdzXG4gICAgICAgICAgICAgICAgICAgICAgICBjb25maWd1cmF0aW9uLCA8Y29kZSBjc3M9e3R3YGZvbnQtbW9ubyBiZy1uZXV0cmFsLTkwMGB9PmNvbmZpZy55bWw8L2NvZGU+LCBtaWdodCBoZWxwIHJlc29sdmVcbiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMgaXNzdWUuXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY3NzPXt0d2BtdC00YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Yj5Ob3RlOiBXaW5ncyBtdXN0IGJlIHJlc3RhcnRlZCBmb3IgdGhlIGNvbmZpZ3VyYXRpb24gZmlsZSBjaGFuZ2VzIHRvIHRha2UgZWZmZWN0PC9iPlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY3NzPXt0d2BtdC04IHNtOmZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktZW5kYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9eygpID0+IHNldFZpc2libGUoZmFsc2UpfSBjc3M9e3R3YHctZnVsbCBzbTp3LWF1dG8gYm9yZGVyLXRyYW5zcGFyZW50YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgQ2xvc2VcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjc3M9e3R3YG10LTQgc206ZmxleCBpdGVtcy1jZW50ZXJgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxGb250QXdlc29tZUljb24gY3NzPXt0d2Bwci00YH0gaWNvbj17ZmFFeGNsYW1hdGlvblRyaWFuZ2xlfSBjb2xvcj17J29yYW5nZSd9IHNpemU9eyc0eCd9IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDIgY3NzPXt0d2B0ZXh0LTJ4bCBtYi00IHRleHQtbmV1dHJhbC0xMDBgfT5Qb3NzaWJsZSByZXNvdXJjZSBsaW1pdCByZWFjaGVkLi4uPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNzcz17dHdgbXQtNGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgVGhpcyBzZXJ2ZXIgaXMgYXR0ZW1wdGluZyB0byB1c2UgbW9yZSByZXNvdXJjZXMgdGhhbiBhbGxvY2F0ZWQuIFBsZWFzZSBjb250YWN0IHRoZSBhZG1pbmlzdHJhdG9yXG4gICAgICAgICAgICAgICAgICAgICAgICBhbmQgZ2l2ZSB0aGVtIHRoZSBlcnJvciBiZWxvdy5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8cCBjc3M9e3R3YG10LTRgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxjb2RlIGNzcz17dHdgZm9udC1tb25vIGJnLW5ldXRyYWwtOTAwYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcHRocmVhZF9jcmVhdGUgZmFpbGVkLCBQb3NzaWJseSBvdXQgb2YgbWVtb3J5IG9yIHByb2Nlc3MvcmVzb3VyY2UgbGltaXRzIHJlYWNoZWRcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvY29kZT5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNzcz17dHdgbXQtOCBzbTpmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWVuZGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBzZXRWaXNpYmxlKGZhbHNlKX0gY3NzPXt0d2B3LWZ1bGwgc206dy1hdXRvIGJvcmRlci10cmFuc3BhcmVudGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENsb3NlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICA8L01vZGFsPlxuICAgICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBQSURMaW1pdE1vZGFsRmVhdHVyZTtcbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./resources/scripts/components/server/features/PIDLimitModalFeature.tsx\n");

/***/ })

}]);