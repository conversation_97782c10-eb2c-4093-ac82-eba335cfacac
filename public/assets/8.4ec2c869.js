(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[8],{

/***/ "./resources/scripts/components/server/features/SteamDiskSpaceFeature.tsx":
/*!********************************************************************************!*\
  !*** ./resources/scripts/components/server/features/SteamDiskSpaceFeature.tsx ***!
  \********************************************************************************/
/*! exports provided: default */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _state_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/state/server */ \"./resources/scripts/state/server/index.ts\");\n/* harmony import */ var _components_elements_Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/elements/Modal */ \"./resources/scripts/components/elements/Modal.tsx\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n/* harmony import */ var _components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/FlashMessageRender */ \"./resources/scripts/components/FlashMessageRender.tsx\");\n/* harmony import */ var _plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/plugins/useFlash */ \"./resources/scripts/plugins/useFlash.ts\");\n/* harmony import */ var _components_server_events__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/server/events */ \"./resources/scripts/components/server/events.ts\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! easy-peasy */ \"./node_modules/easy-peasy/dist/easy-peasy.js\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(easy_peasy__WEBPACK_IMPORTED_MODULE_8__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\nconst SteamDiskSpaceFeature = () => {\n  const [visible, setVisible] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const [loading] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const status = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.status.value);\n  const {\n    clearFlashes\n  } = Object(_plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"])();\n  const {\n    connected,\n    instance\n  } = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.socket);\n  const isAdmin = Object(easy_peasy__WEBPACK_IMPORTED_MODULE_8__[\"useStoreState\"])(state => state.user.data.rootAdmin);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    if (!connected || !instance || status === 'running') return;\n    const errors = ['steamcmd needs 250mb of free disk space to update', '0x202 after update job'];\n\n    const listener = line => {\n      if (errors.some(p => line.toLowerCase().includes(p))) {\n        setVisible(true);\n      }\n    };\n\n    instance.addListener(_components_server_events__WEBPACK_IMPORTED_MODULE_7__[/* SocketEvent */ \"a\"].CONSOLE_OUTPUT, listener);\n    return () => {\n      instance.removeListener(_components_server_events__WEBPACK_IMPORTED_MODULE_7__[/* SocketEvent */ \"a\"].CONSOLE_OUTPUT, listener);\n    };\n  }, [connected, instance, status]);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    clearFlashes('feature:steamDiskSpace');\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Modal__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"b\"], {\n    visible: visible,\n    onDismissed: () => setVisible(false),\n    closeOnBackground: false,\n    showSpinnerOverlay: loading\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledFlashMessageRender, {\n    key: 'feature:steamDiskSpace',\n    \"data-tw\": \"mb-4\"\n  }), isAdmin ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(react__WEBPACK_IMPORTED_MODULE_1___default.a.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv, {\n    \"data-tw\": \"mt-4 sm:flex items-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledH, {\n    \"data-tw\": \"text-2xl mb-4 text-neutral-100\"\n  }, \"Out of available disk space...\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP, {\n    \"data-tw\": \"mt-4\"\n  }, \"This server has run out of available disk space and cannot complete the install or update process.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP2, {\n    \"data-tw\": \"mt-4\"\n  }, \"Ensure the machine has enough disk space by typing\", ' ', /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledCode, {\n    \"data-tw\": \"font-mono bg-neutral-900 rounded py-1 px-2\"\n  }, \"df -h\"), \" on the machine hosting this server. Delete files or increase the available disk space to resolve the issue.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv2, {\n    \"data-tw\": \"mt-8 sm:flex items-center justify-end\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton, {\n    onClick: () => setVisible(false),\n    \"data-tw\": \"w-full sm:w-auto border-transparent\"\n  }, \"Close\"))) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(react__WEBPACK_IMPORTED_MODULE_1___default.a.Fragment, null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv3, {\n    \"data-tw\": \"mt-4 sm:flex items-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledH2, {\n    \"data-tw\": \"text-2xl mb-4 text-neutral-100\"\n  }, \"Out of available disk space...\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP3, {\n    \"data-tw\": \"mt-4\"\n  }, \"This server has run out of available disk space and cannot complete the install or update process. Please get in touch with the administrator(s) and inform them of disk space issues.\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv4, {\n    \"data-tw\": \"mt-8 sm:flex items-center justify-end\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton2, {\n    onClick: () => setVisible(false),\n    \"data-tw\": \"w-full sm:w-auto border-transparent\"\n  }, \"Close\"))));\n};\n\n__signature__(SteamDiskSpaceFeature, \"useState{[visible, setVisible](false)}\\nuseState{[loading](false)}\\nuseStoreState{status}\\nuseFlash{{ clearFlashes }}\\nuseStoreState{{ connected, instance }}\\nuseStoreState{isAdmin}\\nuseEffect{}\\nuseEffect{}\", () => [_state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState, _plugins_useFlash__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"], _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState, easy_peasy__WEBPACK_IMPORTED_MODULE_8__[\"useStoreState\"]]);\n\nconst _default = SteamDiskSpaceFeature;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_default);\n\nvar _StyledFlashMessageRender = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"]).withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledFlashMessageRender\",\n  componentId: \"sc-1ak76ba-0\"\n})({\n  \"marginBottom\": \"1rem\"\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledDiv\",\n  componentId: \"sc-1ak76ba-1\"\n})({\n  \"marginTop\": \"1rem\",\n  \"alignItems\": \"center\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledH = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"h2\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledH\",\n  componentId: \"sc-1ak76ba-2\"\n})({\n  \"fontSize\": \"1.5rem\",\n  \"lineHeight\": \"2rem\",\n  \"marginBottom\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(214, 15%, 91%, var(--tw-text-opacity))\"\n});\n\nvar _StyledP = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledP\",\n  componentId: \"sc-1ak76ba-3\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledP2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledP2\",\n  componentId: \"sc-1ak76ba-4\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledCode = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"code\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledCode\",\n  componentId: \"sc-1ak76ba-5\"\n})({\n  \"fontFamily\": \"ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace\",\n  \"--tw-bg-opacity\": \"1\",\n  \"backgroundColor\": \"hsla(210, 24%, 16%, var(--tw-bg-opacity))\",\n  \"borderRadius\": \"0.25rem\",\n  \"paddingTop\": \"0.25rem\",\n  \"paddingBottom\": \"0.25rem\",\n  \"paddingLeft\": \"0.5rem\",\n  \"paddingRight\": \"0.5rem\"\n});\n\nvar _StyledDiv2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledDiv2\",\n  componentId: \"sc-1ak76ba-6\"\n})({\n  \"marginTop\": \"2rem\",\n  \"alignItems\": \"center\",\n  \"justifyContent\": \"flex-end\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledButton = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"]).withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledButton\",\n  componentId: \"sc-1ak76ba-7\"\n})({\n  \"width\": \"100%\",\n  \"borderColor\": \"rgba(0, 0, 0, 0)\",\n  \"@media (min-width: 640px)\": {\n    \"width\": \"auto\"\n  }\n});\n\nvar _StyledDiv3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledDiv3\",\n  componentId: \"sc-1ak76ba-8\"\n})({\n  \"marginTop\": \"1rem\",\n  \"alignItems\": \"center\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledH2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"h2\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledH2\",\n  componentId: \"sc-1ak76ba-9\"\n})({\n  \"fontSize\": \"1.5rem\",\n  \"lineHeight\": \"2rem\",\n  \"marginBottom\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(214, 15%, 91%, var(--tw-text-opacity))\"\n});\n\nvar _StyledP3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledP3\",\n  componentId: \"sc-1ak76ba-10\"\n})({\n  \"marginTop\": \"1rem\"\n});\n\nvar _StyledDiv4 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledDiv4\",\n  componentId: \"sc-1ak76ba-11\"\n})({\n  \"marginTop\": \"2rem\",\n  \"alignItems\": \"center\",\n  \"justifyContent\": \"flex-end\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledButton2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"]).withConfig({\n  displayName: \"SteamDiskSpaceFeature___StyledButton2\",\n  componentId: \"sc-1ak76ba-12\"\n})({\n  \"width\": \"100%\",\n  \"borderColor\": \"rgba(0, 0, 0, 0)\",\n  \"@media (min-width: 640px)\": {\n    \"width\": \"auto\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(SteamDiskSpaceFeature, \"SteamDiskSpaceFeature\", \"/workspaces/Pterod/resources/scripts/components/server/features/SteamDiskSpaceFeature.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/server/features/SteamDiskSpaceFeature.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9jb21wb25lbnRzL3NlcnZlci9mZWF0dXJlcy9TdGVhbURpc2tTcGFjZUZlYXR1cmUudHN4P2NmODgiXSwibmFtZXMiOlsiU3RlYW1EaXNrU3BhY2VGZWF0dXJlIiwidmlzaWJsZSIsInNldFZpc2libGUiLCJ1c2VTdGF0ZSIsImxvYWRpbmciLCJzdGF0dXMiLCJTZXJ2ZXJDb250ZXh0IiwidXNlU3RvcmVTdGF0ZSIsInN0YXRlIiwidmFsdWUiLCJjbGVhckZsYXNoZXMiLCJ1c2VGbGFzaCIsImNvbm5lY3RlZCIsImluc3RhbmNlIiwic29ja2V0IiwiaXNBZG1pbiIsInVzZXIiLCJkYXRhIiwicm9vdEFkbWluIiwidXNlRWZmZWN0IiwiZXJyb3JzIiwibGlzdGVuZXIiLCJsaW5lIiwic29tZSIsInAiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwiYWRkTGlzdGVuZXIiLCJTb2NrZXRFdmVudCIsIkNPTlNPTEVfT1VUUFVUIiwicmVtb3ZlTGlzdGVuZXIiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxNQUFNQSxxQkFBcUIsR0FBRyxNQUFNO0VBQ2hDLE1BQU0sQ0FBQ0MsT0FBRCxFQUFVQyxVQUFWLElBQXdCQyxzREFBUSxDQUFDLEtBQUQsQ0FBdEM7RUFDQSxNQUFNLENBQUNDLE9BQUQsSUFBWUQsc0RBQVEsQ0FBQyxLQUFELENBQTFCO0VBRUEsTUFBTUUsTUFBTSxHQUFHQyxtRUFBYSxDQUFDQyxhQUFkLENBQTZCQyxLQUFELElBQVdBLEtBQUssQ0FBQ0gsTUFBTixDQUFhSSxLQUFwRCxDQUFmO0VBQ0EsTUFBTTtJQUFFQztFQUFGLElBQW1CQyx5RUFBUSxFQUFqQztFQUNBLE1BQU07SUFBRUMsU0FBRjtJQUFhQztFQUFiLElBQTBCUCxtRUFBYSxDQUFDQyxhQUFkLENBQTZCQyxLQUFELElBQVdBLEtBQUssQ0FBQ00sTUFBN0MsQ0FBaEM7RUFDQSxNQUFNQyxPQUFPLEdBQUdSLGdFQUFhLENBQUVDLEtBQUQsSUFBV0EsS0FBSyxDQUFDUSxJQUFOLENBQVdDLElBQVgsQ0FBaUJDLFNBQTdCLENBQTdCO0VBRUFDLHVEQUFTLENBQUMsTUFBTTtJQUNaLElBQUksQ0FBQ1AsU0FBRCxJQUFjLENBQUNDLFFBQWYsSUFBMkJSLE1BQU0sS0FBSyxTQUExQyxFQUFxRDtJQUVyRCxNQUFNZSxNQUFNLEdBQUcsQ0FBQyxtREFBRCxFQUFzRCx3QkFBdEQsQ0FBZjs7SUFFQSxNQUFNQyxRQUFRLEdBQUlDLElBQUQsSUFBa0I7TUFDL0IsSUFBSUYsTUFBTSxDQUFDRyxJQUFQLENBQWFDLENBQUQsSUFBT0YsSUFBSSxDQUFDRyxXQUFMLEdBQW1CQyxRQUFuQixDQUE0QkYsQ0FBNUIsQ0FBbkIsQ0FBSixFQUF3RDtRQUNwRHRCLFVBQVUsQ0FBQyxJQUFELENBQVY7TUFDSDtJQUNKLENBSkQ7O0lBTUFXLFFBQVEsQ0FBQ2MsV0FBVCxDQUFxQkMsNkVBQVcsQ0FBQ0MsY0FBakMsRUFBaURSLFFBQWpEO0lBRUEsT0FBTyxNQUFNO01BQ1RSLFFBQVEsQ0FBQ2lCLGNBQVQsQ0FBd0JGLDZFQUFXLENBQUNDLGNBQXBDLEVBQW9EUixRQUFwRDtJQUNILENBRkQ7RUFHSCxDQWhCUSxFQWdCTixDQUFDVCxTQUFELEVBQVlDLFFBQVosRUFBc0JSLE1BQXRCLENBaEJNLENBQVQ7RUFrQkFjLHVEQUFTLENBQUMsTUFBTTtJQUNaVCxZQUFZLENBQUMsd0JBQUQsQ0FBWjtFQUNILENBRlEsRUFFTixFQUZNLENBQVQ7RUFJQSxvQkFDSSwyREFBQywwRUFBRDtJQUNJLE9BQU8sRUFBRVQsT0FEYjtJQUVJLFdBQVcsRUFBRSxNQUFNQyxVQUFVLENBQUMsS0FBRCxDQUZqQztJQUdJLGlCQUFpQixFQUFFLEtBSHZCO0lBSUksa0JBQWtCLEVBQUVFO0VBSnhCLGdCQU1JO0lBQW9CLEdBQUcsRUFBRSx3QkFBekI7SUFBQTtFQUFBLEVBTkosRUFPS1csT0FBTyxnQkFDSixxSUFDSTtJQUFBO0VBQUEsZ0JBQ0k7SUFBQTtFQUFBLG9DQURKLENBREosZUFJSTtJQUFBO0VBQUEsd0dBSkosZUFRSTtJQUFBO0VBQUEseURBQ3VELEdBRHZELGVBRUk7SUFBQTtFQUFBLFdBRkosaUhBUkosZUFhSTtJQUFBO0VBQUEsZ0JBQ0k7SUFBUSxPQUFPLEVBQUUsTUFBTWIsVUFBVSxDQUFDLEtBQUQsQ0FBakM7SUFBQTtFQUFBLFdBREosQ0FiSixDQURJLGdCQXFCSixxSUFDSTtJQUFBO0VBQUEsZ0JBQ0k7SUFBQTtFQUFBLG9DQURKLENBREosZUFJSTtJQUFBO0VBQUEsNExBSkosZUFRSTtJQUFBO0VBQUEsZ0JBQ0k7SUFBUSxPQUFPLEVBQUUsTUFBTUEsVUFBVSxDQUFDLEtBQUQsQ0FBakM7SUFBQTtFQUFBLFdBREosQ0FSSixDQTVCUixDQURKO0FBOENILENBN0VEOztjQUFNRixxQiw0TkFJYU0sbUVBQWEsQ0FBQ0MsYSxFQUNKSSxpRSxFQUNPTCxtRUFBYSxDQUFDQyxhLEVBQzlCQSx3RDs7aUJBd0VMUCxxQjtBQUFBOzs7OztHQXpDdUQ7RUFBQTtBQUFBLEM7Ozs7O0dBR3RDO0VBQUE7RUFBQTtFQUFBO0lBQUE7RUFBQTtBQUFBLEM7Ozs7O0dBQ0c7RUFBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FFTDtFQUFBO0FBQUEsQzs7Ozs7R0FJQTtFQUFBO0FBQUEsQzs7Ozs7R0FFTztFQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7QUFBQSxDOzs7OztHQUdMO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FDeUM7RUFBQTtFQUFBO0VBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FPekM7RUFBQTtFQUFBO0VBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FDRztFQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7QUFBQSxDOzs7OztHQUVMO0VBQUE7QUFBQSxDOzs7OztHQUlFO0VBQUE7RUFBQTtFQUFBO0VBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FDeUM7RUFBQTtFQUFBO0VBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7Ozs7Ozs7MEJBckVuRUEscUIiLCJmaWxlIjoiLi9yZXNvdXJjZXMvc2NyaXB0cy9jb21wb25lbnRzL3NlcnZlci9mZWF0dXJlcy9TdGVhbURpc2tTcGFjZUZlYXR1cmUudHN4LmpzIiwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTZXJ2ZXJDb250ZXh0IH0gZnJvbSAnQC9zdGF0ZS9zZXJ2ZXInO1xuaW1wb3J0IE1vZGFsIGZyb20gJ0AvY29tcG9uZW50cy9lbGVtZW50cy9Nb2RhbCc7XG5pbXBvcnQgdHcgZnJvbSAndHdpbi5tYWNybyc7XG5pbXBvcnQgQnV0dG9uIGZyb20gJ0AvY29tcG9uZW50cy9lbGVtZW50cy9CdXR0b24nO1xuaW1wb3J0IEZsYXNoTWVzc2FnZVJlbmRlciBmcm9tICdAL2NvbXBvbmVudHMvRmxhc2hNZXNzYWdlUmVuZGVyJztcbmltcG9ydCB1c2VGbGFzaCBmcm9tICdAL3BsdWdpbnMvdXNlRmxhc2gnO1xuaW1wb3J0IHsgU29ja2V0RXZlbnQgfSBmcm9tICdAL2NvbXBvbmVudHMvc2VydmVyL2V2ZW50cyc7XG5pbXBvcnQgeyB1c2VTdG9yZVN0YXRlIH0gZnJvbSAnZWFzeS1wZWFzeSc7XG5cbmNvbnN0IFN0ZWFtRGlza1NwYWNlRmVhdHVyZSA9ICgpID0+IHtcbiAgICBjb25zdCBbdmlzaWJsZSwgc2V0VmlzaWJsZV0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gICAgY29uc3QgW2xvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gICAgY29uc3Qgc3RhdHVzID0gU2VydmVyQ29udGV4dC51c2VTdG9yZVN0YXRlKChzdGF0ZSkgPT4gc3RhdGUuc3RhdHVzLnZhbHVlKTtcbiAgICBjb25zdCB7IGNsZWFyRmxhc2hlcyB9ID0gdXNlRmxhc2goKTtcbiAgICBjb25zdCB7IGNvbm5lY3RlZCwgaW5zdGFuY2UgfSA9IFNlcnZlckNvbnRleHQudXNlU3RvcmVTdGF0ZSgoc3RhdGUpID0+IHN0YXRlLnNvY2tldCk7XG4gICAgY29uc3QgaXNBZG1pbiA9IHVzZVN0b3JlU3RhdGUoKHN0YXRlKSA9PiBzdGF0ZS51c2VyLmRhdGEhLnJvb3RBZG1pbik7XG5cbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoIWNvbm5lY3RlZCB8fCAhaW5zdGFuY2UgfHwgc3RhdHVzID09PSAncnVubmluZycpIHJldHVybjtcblxuICAgICAgICBjb25zdCBlcnJvcnMgPSBbJ3N0ZWFtY21kIG5lZWRzIDI1MG1iIG9mIGZyZWUgZGlzayBzcGFjZSB0byB1cGRhdGUnLCAnMHgyMDIgYWZ0ZXIgdXBkYXRlIGpvYiddO1xuXG4gICAgICAgIGNvbnN0IGxpc3RlbmVyID0gKGxpbmU6IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgaWYgKGVycm9ycy5zb21lKChwKSA9PiBsaW5lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMocCkpKSB7XG4gICAgICAgICAgICAgICAgc2V0VmlzaWJsZSh0cnVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcblxuICAgICAgICBpbnN0YW5jZS5hZGRMaXN0ZW5lcihTb2NrZXRFdmVudC5DT05TT0xFX09VVFBVVCwgbGlzdGVuZXIpO1xuXG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICBpbnN0YW5jZS5yZW1vdmVMaXN0ZW5lcihTb2NrZXRFdmVudC5DT05TT0xFX09VVFBVVCwgbGlzdGVuZXIpO1xuICAgICAgICB9O1xuICAgIH0sIFtjb25uZWN0ZWQsIGluc3RhbmNlLCBzdGF0dXNdKTtcblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNsZWFyRmxhc2hlcygnZmVhdHVyZTpzdGVhbURpc2tTcGFjZScpO1xuICAgIH0sIFtdKTtcblxuICAgIHJldHVybiAoXG4gICAgICAgIDxNb2RhbFxuICAgICAgICAgICAgdmlzaWJsZT17dmlzaWJsZX1cbiAgICAgICAgICAgIG9uRGlzbWlzc2VkPXsoKSA9PiBzZXRWaXNpYmxlKGZhbHNlKX1cbiAgICAgICAgICAgIGNsb3NlT25CYWNrZ3JvdW5kPXtmYWxzZX1cbiAgICAgICAgICAgIHNob3dTcGlubmVyT3ZlcmxheT17bG9hZGluZ31cbiAgICAgICAgPlxuICAgICAgICAgICAgPEZsYXNoTWVzc2FnZVJlbmRlciBrZXk9eydmZWF0dXJlOnN0ZWFtRGlza1NwYWNlJ30gY3NzPXt0d2BtYi00YH0gLz5cbiAgICAgICAgICAgIHtpc0FkbWluID8gKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY3NzPXt0d2BtdC00IHNtOmZsZXggaXRlbXMtY2VudGVyYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDIgY3NzPXt0d2B0ZXh0LTJ4bCBtYi00IHRleHQtbmV1dHJhbC0xMDAgYH0+T3V0IG9mIGF2YWlsYWJsZSBkaXNrIHNwYWNlLi4uPC9oMj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNzcz17dHdgbXQtNGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgVGhpcyBzZXJ2ZXIgaGFzIHJ1biBvdXQgb2YgYXZhaWxhYmxlIGRpc2sgc3BhY2UgYW5kIGNhbm5vdCBjb21wbGV0ZSB0aGUgaW5zdGFsbCBvciB1cGRhdGVcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2Nlc3MuXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY3NzPXt0d2BtdC00YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICBFbnN1cmUgdGhlIG1hY2hpbmUgaGFzIGVub3VnaCBkaXNrIHNwYWNlIGJ5IHR5cGluZ3snICd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8Y29kZSBjc3M9e3R3YGZvbnQtbW9ubyBiZy1uZXV0cmFsLTkwMCByb3VuZGVkIHB5LTEgcHgtMmB9PmRmIC1oPC9jb2RlPiBvbiB0aGUgbWFjaGluZSBob3N0aW5nXG4gICAgICAgICAgICAgICAgICAgICAgICB0aGlzIHNlcnZlci4gRGVsZXRlIGZpbGVzIG9yIGluY3JlYXNlIHRoZSBhdmFpbGFibGUgZGlzayBzcGFjZSB0byByZXNvbHZlIHRoZSBpc3N1ZS5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNzcz17dHdgbXQtOCBzbTpmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWVuZGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBzZXRWaXNpYmxlKGZhbHNlKX0gY3NzPXt0d2B3LWZ1bGwgc206dy1hdXRvIGJvcmRlci10cmFuc3BhcmVudGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENsb3NlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY3NzPXt0d2BtdC00IHNtOmZsZXggaXRlbXMtY2VudGVyYH0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDIgY3NzPXt0d2B0ZXh0LTJ4bCBtYi00IHRleHQtbmV1dHJhbC0xMDBgfT5PdXQgb2YgYXZhaWxhYmxlIGRpc2sgc3BhY2UuLi48L2gyPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY3NzPXt0d2BtdC00YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICBUaGlzIHNlcnZlciBoYXMgcnVuIG91dCBvZiBhdmFpbGFibGUgZGlzayBzcGFjZSBhbmQgY2Fubm90IGNvbXBsZXRlIHRoZSBpbnN0YWxsIG9yIHVwZGF0ZVxuICAgICAgICAgICAgICAgICAgICAgICAgcHJvY2Vzcy4gUGxlYXNlIGdldCBpbiB0b3VjaCB3aXRoIHRoZSBhZG1pbmlzdHJhdG9yKHMpIGFuZCBpbmZvcm0gdGhlbSBvZiBkaXNrIHNwYWNlIGlzc3Vlcy5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNzcz17dHdgbXQtOCBzbTpmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWVuZGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXsoKSA9PiBzZXRWaXNpYmxlKGZhbHNlKX0gY3NzPXt0d2B3LWZ1bGwgc206dy1hdXRvIGJvcmRlci10cmFuc3BhcmVudGB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIENsb3NlXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICA8L01vZGFsPlxuICAgICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBTdGVhbURpc2tTcGFjZUZlYXR1cmU7XG4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./resources/scripts/components/server/features/SteamDiskSpaceFeature.tsx\n");

/***/ })

}]);