(window["webpackJsonp"] = window["webpackJsonp"] || []).push([["auth"],{

/***/ "./resources/scripts/api/auth/login.ts":
/*!*********************************************!*\
  !*** ./resources/scripts/api/auth/login.ts ***!
  \*********************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\nconst _default = _ref => {\n  let {\n    username,\n    password,\n    recaptchaData\n  } = _ref;\n  return new Promise((resolve, reject) => {\n    _api_http__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"a\"].get('/sanctum/csrf-cookie').then(() => _api_http__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"a\"].post('/auth/login', {\n      user: username,\n      password,\n      'g-recaptcha-response': recaptchaData\n    })).then(response => {\n      if (!(response.data instanceof Object)) {\n        return reject(new Error('An error occurred while processing the login request.'));\n      }\n\n      return resolve({\n        complete: response.data.data.complete,\n        intended: response.data.data.intended || undefined,\n        confirmationToken: response.data.data.confirmation_token || undefined\n      });\n    }).catch(reject);\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/api/auth/login.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/api/auth/login.ts\n");

/***/ }),

/***/ "./resources/scripts/api/auth/loginCheckpoint.ts":
/*!*******************************************************!*\
  !*** ./resources/scripts/api/auth/loginCheckpoint.ts ***!
  \*******************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\nconst _default = (token, code, recoveryToken) => {\n  return new Promise((resolve, reject) => {\n    _api_http__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"a\"].post('/auth/login/checkpoint', {\n      confirmation_token: token,\n      authentication_code: code,\n      recovery_token: recoveryToken && recoveryToken.length > 0 ? recoveryToken : undefined\n    }).then(response => resolve({\n      complete: response.data.data.complete,\n      intended: response.data.data.intended || undefined\n    })).catch(reject);\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/api/auth/loginCheckpoint.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/api/auth/loginCheckpoint.ts\n");

/***/ }),

/***/ "./resources/scripts/api/auth/performPasswordReset.ts":
/*!************************************************************!*\
  !*** ./resources/scripts/api/auth/performPasswordReset.ts ***!
  \************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\nconst _default = (email, data) => {\n  return new Promise((resolve, reject) => {\n    _api_http__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"a\"].post('/auth/password/reset', {\n      email,\n      token: data.token,\n      password: data.password,\n      password_confirmation: data.passwordConfirmation\n    }).then(response => resolve({\n      redirectTo: response.data.redirect_to,\n      sendToLogin: response.data.send_to_login\n    })).catch(reject);\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/api/auth/performPasswordReset.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/api/auth/performPasswordReset.ts\n");

/***/ }),

/***/ "./resources/scripts/api/auth/requestPasswordResetEmail.ts":
/*!*****************************************************************!*\
  !*** ./resources/scripts/api/auth/requestPasswordResetEmail.ts ***!
  \*****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\nconst _default = (email, recaptchaData) => {\n  return new Promise((resolve, reject) => {\n    _api_http__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"a\"].post('/auth/password', {\n      email,\n      'g-recaptcha-response': recaptchaData\n    }).then(response => resolve(response.data.status || '')).catch(reject);\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/api/auth/requestPasswordResetEmail.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9hcGkvYXV0aC9yZXF1ZXN0UGFzc3dvcmRSZXNldEVtYWlsLnRzPzY0M2YiXSwibmFtZXMiOlsiZW1haWwiLCJyZWNhcHRjaGFEYXRhIiwiUHJvbWlzZSIsInJlc29sdmUiLCJyZWplY3QiLCJodHRwIiwicG9zdCIsInRoZW4iLCJyZXNwb25zZSIsImRhdGEiLCJzdGF0dXMiLCJjYXRjaCJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBOztpQkFFZSxDQUFDQSxLQUFELEVBQWdCQyxhQUFoQixLQUE0RDtFQUN2RSxPQUFPLElBQUlDLE9BQUosQ0FBWSxDQUFDQyxPQUFELEVBQVVDLE1BQVYsS0FBcUI7SUFDcENDLHlEQUFJLENBQUNDLElBQUwsQ0FBVSxnQkFBVixFQUE0QjtNQUFFTixLQUFGO01BQVMsd0JBQXdCQztJQUFqQyxDQUE1QixFQUNLTSxJQURMLENBQ1dDLFFBQUQsSUFBY0wsT0FBTyxDQUFDSyxRQUFRLENBQUNDLElBQVQsQ0FBY0MsTUFBZCxJQUF3QixFQUF6QixDQUQvQixFQUVLQyxLQUZMLENBRVdQLE1BRlg7RUFHSCxDQUpNLENBQVA7QUFLSCxDOztBQU5jIiwiZmlsZSI6Ii4vcmVzb3VyY2VzL3NjcmlwdHMvYXBpL2F1dGgvcmVxdWVzdFBhc3N3b3JkUmVzZXRFbWFpbC50cy5qcyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBodHRwIGZyb20gJ0AvYXBpL2h0dHAnO1xuXG5leHBvcnQgZGVmYXVsdCAoZW1haWw6IHN0cmluZywgcmVjYXB0Y2hhRGF0YT86IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiA9PiB7XG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgaHR0cC5wb3N0KCcvYXV0aC9wYXNzd29yZCcsIHsgZW1haWwsICdnLXJlY2FwdGNoYS1yZXNwb25zZSc6IHJlY2FwdGNoYURhdGEgfSlcbiAgICAgICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4gcmVzb2x2ZShyZXNwb25zZS5kYXRhLnN0YXR1cyB8fCAnJykpXG4gICAgICAgICAgICAuY2F0Y2gocmVqZWN0KTtcbiAgICB9KTtcbn07XG4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./resources/scripts/api/auth/requestPasswordResetEmail.ts\n");

/***/ }),

/***/ "./resources/scripts/components/auth/ForgotPasswordContainer.tsx":
/*!***********************************************************************!*\
  !*** ./resources/scripts/components/auth/ForgotPasswordContainer.tsx ***!
  \***********************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-router-dom */ \"./node_modules/react-router-dom/esm/react-router-dom.js\");\n/* harmony import */ var _api_auth_requestPasswordResetEmail__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/auth/requestPasswordResetEmail */ \"./resources/scripts/api/auth/requestPasswordResetEmail.ts\");\n/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n/* harmony import */ var _components_auth_LoginFormContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/LoginFormContainer */ \"./resources/scripts/components/auth/LoginFormContainer.tsx\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! easy-peasy */ \"./node_modules/easy-peasy/dist/easy-peasy.js\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(easy_peasy__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _components_elements_Field__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/elements/Field */ \"./resources/scripts/components/elements/Field.tsx\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! formik */ \"./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! yup */ \"./node_modules/yup/es/index.js\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n/* harmony import */ var reaptcha__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reaptcha */ \"./node_modules/reaptcha/dist/index.js\");\n/* harmony import */ var reaptcha__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(reaptcha__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _plugins_useFlash__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/plugins/useFlash */ \"./resources/scripts/plugins/useFlash.ts\");\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst _default = () => {\n  const ref = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useRef\"])(null);\n  const [token, setToken] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])('');\n  const {\n    clearFlashes,\n    addFlash\n  } = Object(_plugins_useFlash__WEBPACK_IMPORTED_MODULE_12__[/* default */ \"a\"])();\n  const {\n    enabled: recaptchaEnabled,\n    siteKey\n  } = Object(easy_peasy__WEBPACK_IMPORTED_MODULE_6__[\"useStoreState\"])(state => state.settings.data.recaptcha);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    clearFlashes();\n  }, []);\n\n  const handleSubmission = (_ref, _ref2) => {\n    let {\n      email\n    } = _ref;\n    let {\n      setSubmitting,\n      resetForm\n    } = _ref2;\n    clearFlashes(); // If there is no token in the state yet, request the token and then abort this submit request\n    // since it will be re-submitted when the recaptcha data is returned by the component.\n\n    if (recaptchaEnabled && !token) {\n      ref.current.execute().catch(error => {\n        console.error(error);\n        setSubmitting(false);\n        addFlash({\n          type: 'error',\n          title: 'Error',\n          message: Object(_api_http__WEBPACK_IMPORTED_MODULE_4__[/* httpErrorToHuman */ \"c\"])(error)\n        });\n      });\n      return;\n    }\n\n    Object(_api_auth_requestPasswordResetEmail__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"a\"])(email, token).then(response => {\n      resetForm();\n      addFlash({\n        type: 'success',\n        title: 'Success',\n        message: response\n      });\n    }).catch(error => {\n      console.error(error);\n      addFlash({\n        type: 'error',\n        title: 'Error',\n        message: Object(_api_http__WEBPACK_IMPORTED_MODULE_4__[/* httpErrorToHuman */ \"c\"])(error)\n      });\n    }).then(() => {\n      setToken('');\n      if (ref.current) ref.current.reset();\n      setSubmitting(false);\n    });\n  };\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__[\"createElement\"](formik__WEBPACK_IMPORTED_MODULE_8__[/* Formik */ \"c\"], {\n    onSubmit: handleSubmission,\n    initialValues: {\n      email: ''\n    },\n    validationSchema: Object(yup__WEBPACK_IMPORTED_MODULE_9__[/* object */ \"d\"])().shape({\n      email: Object(yup__WEBPACK_IMPORTED_MODULE_9__[/* string */ \"f\"])().email('A valid email address must be provided to continue.').required('A valid email address must be provided to continue.')\n    })\n  }, _ref3 => {\n    let {\n      isSubmitting,\n      setSubmitting,\n      submitForm\n    } = _ref3;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__[\"createElement\"](_StyledLoginFormContainer, {\n      title: 'Request Password Reset',\n      \"data-tw\": \"w-full flex\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__[\"createElement\"](_components_elements_Field__WEBPACK_IMPORTED_MODULE_7__[/* default */ \"a\"], {\n      light: true,\n      label: 'Email',\n      description: 'Enter your account email address to receive instructions on resetting your password.',\n      name: 'email',\n      type: 'email'\n    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__[\"createElement\"](_StyledDiv, {\n      \"data-tw\": \"mt-6\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__[\"createElement\"](_components_elements_Button__WEBPACK_IMPORTED_MODULE_10__[/* default */ \"a\"], {\n      type: 'submit',\n      size: 'xlarge',\n      disabled: isSubmitting,\n      isLoading: isSubmitting\n    }, \"Send Email\")), recaptchaEnabled && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__[\"createElement\"](reaptcha__WEBPACK_IMPORTED_MODULE_11___default.a, {\n      ref: ref,\n      size: 'invisible',\n      sitekey: siteKey || '_invalid_key',\n      onVerify: response => {\n        setToken(response);\n        submitForm();\n      },\n      onExpire: () => {\n        setSubmitting(false);\n        setToken('');\n      }\n    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__[\"createElement\"](_StyledDiv2, {\n      \"data-tw\": \"mt-6 text-center\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1__[\"createElement\"](_StyledLink, {\n      to: '/auth/login',\n      \"data-tw\": \"text-xs text-neutral-500 tracking-wide uppercase no-underline hover:text-neutral-700\"\n    }, \"Return to Login\")));\n  });\n};\n\n__signature__(_default, \"useRef{ref}\\nuseState{[token, setToken]('')}\\nuseFlash{{ clearFlashes, addFlash }}\\nuseStoreState{{ enabled: recaptchaEnabled, siteKey }}\\nuseEffect{}\", () => [_plugins_useFlash__WEBPACK_IMPORTED_MODULE_12__[/* default */ \"a\"], easy_peasy__WEBPACK_IMPORTED_MODULE_6__[\"useStoreState\"]]);\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n\nvar _StyledLoginFormContainer = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_auth_LoginFormContainer__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"]).withConfig({\n  displayName: \"ForgotPasswordContainer___StyledLoginFormContainer\",\n  componentId: \"sc-1crept1-0\"\n})({\n  \"width\": \"100%\",\n  \"display\": \"flex\"\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"ForgotPasswordContainer___StyledDiv\",\n  componentId: \"sc-1crept1-1\"\n})({\n  \"marginTop\": \"1.5rem\"\n});\n\nvar _StyledDiv2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"ForgotPasswordContainer___StyledDiv2\",\n  componentId: \"sc-1crept1-2\"\n})({\n  \"marginTop\": \"1.5rem\",\n  \"textAlign\": \"center\"\n});\n\nvar _StyledLink = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(react_router_dom__WEBPACK_IMPORTED_MODULE_2__[/* Link */ \"a\"]).withConfig({\n  displayName: \"ForgotPasswordContainer___StyledLink\",\n  componentId: \"sc-1crept1-3\"\n})({\n  \"fontSize\": \"0.75rem\",\n  \"lineHeight\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(211, 12%, 43%, var(--tw-text-opacity))\",\n  \"letterSpacing\": \"0.025em\",\n  \"textTransform\": \"uppercase\",\n  \"textDecoration\": \"none\",\n  \":hover\": {\n    \"--tw-text-opacity\": \"1\",\n    \"color\": \"hsla(209, 18%, 30%, var(--tw-text-opacity))\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/auth/ForgotPasswordContainer.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/auth/ForgotPasswordContainer.tsx\n");

/***/ }),

/***/ "./resources/scripts/components/auth/LoginCheckpointContainer.tsx":
/*!************************************************************************!*\
  !*** ./resources/scripts/components/auth/LoginCheckpointContainer.tsx ***!
  \************************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-router-dom */ \"./node_modules/react-router-dom/esm/react-router-dom.js\");\n/* harmony import */ var _api_auth_loginCheckpoint__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/api/auth/loginCheckpoint */ \"./resources/scripts/api/auth/loginCheckpoint.ts\");\n/* harmony import */ var _components_auth_LoginFormContainer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/auth/LoginFormContainer */ \"./resources/scripts/components/auth/LoginFormContainer.tsx\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! formik */ \"./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _plugins_useFlash__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/plugins/useFlash */ \"./resources/scripts/plugins/useFlash.ts\");\n/* harmony import */ var _components_elements_Field__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/elements/Field */ \"./resources/scripts/components/elements/Field.tsx\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n\n\n\n\n\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\nconst LoginCheckpointContainer = () => {\n  const {\n    isSubmitting,\n    setFieldValue\n  } = Object(formik__WEBPACK_IMPORTED_MODULE_7__[/* useFormikContext */ \"e\"])();\n  const [isMissingDevice, setIsMissingDevice] = Object(react__WEBPACK_IMPORTED_MODULE_3__[\"useState\"])(false);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_StyledLoginFormContainer, {\n    title: 'Device Checkpoint',\n    \"data-tw\": \"w-full flex\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_StyledDiv, {\n    \"data-tw\": \"mt-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_components_elements_Field__WEBPACK_IMPORTED_MODULE_9__[/* default */ \"a\"], {\n    light: true,\n    name: isMissingDevice ? 'recoveryCode' : 'code',\n    title: isMissingDevice ? 'Recovery Code' : 'Authentication Code',\n    description: isMissingDevice ? 'Enter one of the recovery codes generated when you setup 2-Factor authentication on this account in order to continue.' : 'Enter the two-factor token generated by your device.',\n    type: 'text',\n    autoComplete: 'one-time-code',\n    autoFocus: true\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_StyledDiv2, {\n    \"data-tw\": \"mt-6\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_components_elements_Button__WEBPACK_IMPORTED_MODULE_10__[/* default */ \"a\"], {\n    size: 'xlarge',\n    type: 'submit',\n    disabled: isSubmitting,\n    isLoading: isSubmitting\n  }, \"Continue\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_StyledDiv3, {\n    \"data-tw\": \"mt-6 text-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_StyledSpan, {\n    onClick: () => {\n      setFieldValue('code', '');\n      setFieldValue('recoveryCode', '');\n      setIsMissingDevice(s => !s);\n    },\n    \"data-tw\": \"cursor-pointer text-xs text-neutral-500 tracking-wide uppercase no-underline hover:text-neutral-700\"\n  }, !isMissingDevice ? \"I've Lost My Device\" : 'I Have My Device')), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_StyledDiv4, {\n    \"data-tw\": \"mt-6 text-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(_StyledLink, {\n    to: '/auth/login',\n    \"data-tw\": \"text-xs text-neutral-500 tracking-wide uppercase no-underline hover:text-neutral-700\"\n  }, \"Return to Login\")));\n};\n\n__signature__(LoginCheckpointContainer, \"useFormikContext{{ isSubmitting, setFieldValue }}\\nuseState{[isMissingDevice, setIsMissingDevice](false)}\", () => [formik__WEBPACK_IMPORTED_MODULE_7__[/* useFormikContext */ \"e\"]]);\n\nconst EnhancedForm = Object(formik__WEBPACK_IMPORTED_MODULE_7__[/* withFormik */ \"f\"])({\n  handleSubmit: (_ref, _ref2) => {\n    var _location$state;\n\n    let {\n      code,\n      recoveryCode\n    } = _ref;\n    let {\n      setSubmitting,\n      props: {\n        clearAndAddHttpError,\n        location\n      }\n    } = _ref2;\n    Object(_api_auth_loginCheckpoint__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"])(((_location$state = location.state) === null || _location$state === void 0 ? void 0 : _location$state.token) || '', code, recoveryCode).then(response => {\n      if (response.complete) {\n        // @ts-expect-error this is valid\n        window.location = response.intended || '/';\n        return;\n      }\n\n      setSubmitting(false);\n    }).catch(error => {\n      console.error(error);\n      setSubmitting(false);\n      clearAndAddHttpError({\n        error\n      });\n    });\n  },\n  mapPropsToValues: () => ({\n    code: '',\n    recoveryCode: ''\n  })\n})(LoginCheckpointContainer);\n\nconst _default = _ref3 => {\n  var _location$state2;\n\n  let {\n    history,\n    location\n  } = _ref3,\n      props = _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1___default()(_ref3, [\"history\", \"location\"]);\n\n  const {\n    clearAndAddHttpError\n  } = Object(_plugins_useFlash__WEBPACK_IMPORTED_MODULE_8__[/* default */ \"a\"])();\n\n  if (!((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.token)) {\n    history.replace('/auth/login');\n    return null;\n  }\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default.a.createElement(EnhancedForm, _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({\n    clearAndAddHttpError: clearAndAddHttpError,\n    history: history,\n    location: location\n  }, props));\n};\n\n__signature__(_default, \"useFlash{{ clearAndAddHttpError }}\", () => [_plugins_useFlash__WEBPACK_IMPORTED_MODULE_8__[/* default */ \"a\"]]);\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n\nvar _StyledLoginFormContainer = Object(styled_components__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"c\"])(_components_auth_LoginFormContainer__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"]).withConfig({\n  displayName: \"LoginCheckpointContainer___StyledLoginFormContainer\",\n  componentId: \"sc-1a62wwr-0\"\n})({\n  \"width\": \"100%\",\n  \"display\": \"flex\"\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginCheckpointContainer___StyledDiv\",\n  componentId: \"sc-1a62wwr-1\"\n})({\n  \"marginTop\": \"1.5rem\"\n});\n\nvar _StyledDiv2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginCheckpointContainer___StyledDiv2\",\n  componentId: \"sc-1a62wwr-2\"\n})({\n  \"marginTop\": \"1.5rem\"\n});\n\nvar _StyledDiv3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginCheckpointContainer___StyledDiv3\",\n  componentId: \"sc-1a62wwr-3\"\n})({\n  \"marginTop\": \"1.5rem\",\n  \"textAlign\": \"center\"\n});\n\nvar _StyledSpan = Object(styled_components__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"c\"])(\"span\").withConfig({\n  displayName: \"LoginCheckpointContainer___StyledSpan\",\n  componentId: \"sc-1a62wwr-4\"\n})({\n  \"cursor\": \"pointer\",\n  \"fontSize\": \"0.75rem\",\n  \"lineHeight\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(211, 12%, 43%, var(--tw-text-opacity))\",\n  \"letterSpacing\": \"0.025em\",\n  \"textTransform\": \"uppercase\",\n  \"textDecoration\": \"none\",\n  \":hover\": {\n    \"--tw-text-opacity\": \"1\",\n    \"color\": \"hsla(209, 18%, 30%, var(--tw-text-opacity))\"\n  }\n});\n\nvar _StyledDiv4 = Object(styled_components__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginCheckpointContainer___StyledDiv4\",\n  componentId: \"sc-1a62wwr-5\"\n})({\n  \"marginTop\": \"1.5rem\",\n  \"textAlign\": \"center\"\n});\n\nvar _StyledLink = Object(styled_components__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"c\"])(react_router_dom__WEBPACK_IMPORTED_MODULE_4__[/* Link */ \"a\"]).withConfig({\n  displayName: \"LoginCheckpointContainer___StyledLink\",\n  componentId: \"sc-1a62wwr-6\"\n})({\n  \"fontSize\": \"0.75rem\",\n  \"lineHeight\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(211, 12%, 43%, var(--tw-text-opacity))\",\n  \"letterSpacing\": \"0.025em\",\n  \"textTransform\": \"uppercase\",\n  \"textDecoration\": \"none\",\n  \":hover\": {\n    \"--tw-text-opacity\": \"1\",\n    \"color\": \"hsla(209, 18%, 30%, var(--tw-text-opacity))\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(LoginCheckpointContainer, \"LoginCheckpointContainer\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginCheckpointContainer.tsx\");\n  reactHotLoader.register(EnhancedForm, \"EnhancedForm\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginCheckpointContainer.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginCheckpointContainer.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/auth/LoginCheckpointContainer.tsx\n");

/***/ }),

/***/ "./resources/scripts/components/auth/LoginContainer.tsx":
/*!**************************************************************!*\
  !*** ./resources/scripts/components/auth/LoginContainer.tsx ***!
  \**************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/defineProperty */ \"./node_modules/@babel/runtime/helpers/defineProperty.js\");\n/* harmony import */ var _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-router-dom */ \"./node_modules/react-router-dom/esm/react-router-dom.js\");\n/* harmony import */ var _api_auth_login__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/api/auth/login */ \"./resources/scripts/api/auth/login.ts\");\n/* harmony import */ var _components_auth_LoginFormContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/LoginFormContainer */ \"./resources/scripts/components/auth/LoginFormContainer.tsx\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! easy-peasy */ \"./node_modules/easy-peasy/dist/easy-peasy.js\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(easy_peasy__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! formik */ \"./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! yup */ \"./node_modules/yup/es/index.js\");\n/* harmony import */ var _components_elements_Field__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/elements/Field */ \"./resources/scripts/components/elements/Field.tsx\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n/* harmony import */ var reaptcha__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! reaptcha */ \"./node_modules/reaptcha/dist/index.js\");\n/* harmony import */ var reaptcha__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(reaptcha__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _plugins_useFlash__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/plugins/useFlash */ \"./resources/scripts/plugins/useFlash.ts\");\n\n\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _babel_runtime_helpers_defineProperty__WEBPACK_IMPORTED_MODULE_0___default()(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst LoginContainer = _ref => {\n  let {\n    history\n  } = _ref;\n  const ref = Object(react__WEBPACK_IMPORTED_MODULE_2__[\"useRef\"])(null);\n  const [token, setToken] = Object(react__WEBPACK_IMPORTED_MODULE_2__[\"useState\"])('');\n  const {\n    clearFlashes,\n    clearAndAddHttpError\n  } = Object(_plugins_useFlash__WEBPACK_IMPORTED_MODULE_12__[/* default */ \"a\"])();\n  const {\n    enabled: recaptchaEnabled,\n    siteKey\n  } = Object(easy_peasy__WEBPACK_IMPORTED_MODULE_6__[\"useStoreState\"])(state => state.settings.data.recaptcha);\n  Object(react__WEBPACK_IMPORTED_MODULE_2__[\"useEffect\"])(() => {\n    clearFlashes();\n  }, []);\n\n  const onSubmit = (values, _ref2) => {\n    let {\n      setSubmitting\n    } = _ref2;\n    clearFlashes(); // If there is no token in the state yet, request the token and then abort this submit request\n    // since it will be re-submitted when the recaptcha data is returned by the component.\n\n    if (recaptchaEnabled && !token) {\n      ref.current.execute().catch(error => {\n        console.error(error);\n        setSubmitting(false);\n        clearAndAddHttpError({\n          error\n        });\n      });\n      return;\n    }\n\n    Object(_api_auth_login__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"])(_objectSpread(_objectSpread({}, values), {}, {\n      recaptchaData: token\n    })).then(response => {\n      if (response.complete) {\n        // @ts-expect-error this is valid\n        window.location = response.intended || '/';\n        return;\n      }\n\n      history.replace('/auth/login/checkpoint', {\n        token: response.confirmationToken\n      });\n    }).catch(error => {\n      console.error(error);\n      setToken('');\n      if (ref.current) ref.current.reset();\n      setSubmitting(false);\n      clearAndAddHttpError({\n        error\n      });\n    });\n  };\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(formik__WEBPACK_IMPORTED_MODULE_7__[/* Formik */ \"c\"], {\n    onSubmit: onSubmit,\n    initialValues: {\n      username: '',\n      password: ''\n    },\n    validationSchema: Object(yup__WEBPACK_IMPORTED_MODULE_8__[/* object */ \"d\"])().shape({\n      username: Object(yup__WEBPACK_IMPORTED_MODULE_8__[/* string */ \"f\"])().required('A username or email must be provided.'),\n      password: Object(yup__WEBPACK_IMPORTED_MODULE_8__[/* string */ \"f\"])().required('Please enter your account password.')\n    })\n  }, _ref3 => {\n    let {\n      isSubmitting,\n      setSubmitting,\n      submitForm\n    } = _ref3;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_StyledLoginFormContainer, {\n      title: 'Login to Continue',\n      \"data-tw\": \"w-full flex\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_components_elements_Field__WEBPACK_IMPORTED_MODULE_9__[/* default */ \"a\"], {\n      light: true,\n      type: 'text',\n      label: 'Username or Email',\n      name: 'username',\n      disabled: isSubmitting\n    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_StyledDiv, {\n      \"data-tw\": \"mt-6\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_components_elements_Field__WEBPACK_IMPORTED_MODULE_9__[/* default */ \"a\"], {\n      light: true,\n      type: 'password',\n      label: 'Password',\n      name: 'password',\n      disabled: isSubmitting\n    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_StyledDiv2, {\n      \"data-tw\": \"mt-6\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_components_elements_Button__WEBPACK_IMPORTED_MODULE_10__[/* default */ \"a\"], {\n      type: 'submit',\n      size: 'xlarge',\n      isLoading: isSubmitting,\n      disabled: isSubmitting\n    }, \"Login\")), recaptchaEnabled && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(reaptcha__WEBPACK_IMPORTED_MODULE_11___default.a, {\n      ref: ref,\n      size: 'invisible',\n      sitekey: siteKey || '_invalid_key',\n      onVerify: response => {\n        setToken(response);\n        submitForm();\n      },\n      onExpire: () => {\n        setSubmitting(false);\n        setToken('');\n      }\n    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_StyledDiv3, {\n      \"data-tw\": \"mt-6 text-center\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2___default.a.createElement(_StyledLink, {\n      to: '/auth/password',\n      \"data-tw\": \"text-xs text-neutral-500 tracking-wide no-underline uppercase hover:text-neutral-600\"\n    }, \"Forgot password?\")));\n  });\n};\n\n__signature__(LoginContainer, \"useRef{ref}\\nuseState{[token, setToken]('')}\\nuseFlash{{ clearFlashes, clearAndAddHttpError }}\\nuseStoreState{{ enabled: recaptchaEnabled, siteKey }}\\nuseEffect{}\", () => [_plugins_useFlash__WEBPACK_IMPORTED_MODULE_12__[/* default */ \"a\"], easy_peasy__WEBPACK_IMPORTED_MODULE_6__[\"useStoreState\"]]);\n\nconst _default = LoginContainer;\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n\nvar _StyledLoginFormContainer = Object(styled_components__WEBPACK_IMPORTED_MODULE_1__[/* default */ \"c\"])(_components_auth_LoginFormContainer__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"]).withConfig({\n  displayName: \"LoginContainer___StyledLoginFormContainer\",\n  componentId: \"sc-qtrnpk-0\"\n})({\n  \"width\": \"100%\",\n  \"display\": \"flex\"\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_1__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginContainer___StyledDiv\",\n  componentId: \"sc-qtrnpk-1\"\n})({\n  \"marginTop\": \"1.5rem\"\n});\n\nvar _StyledDiv2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_1__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginContainer___StyledDiv2\",\n  componentId: \"sc-qtrnpk-2\"\n})({\n  \"marginTop\": \"1.5rem\"\n});\n\nvar _StyledDiv3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_1__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginContainer___StyledDiv3\",\n  componentId: \"sc-qtrnpk-3\"\n})({\n  \"marginTop\": \"1.5rem\",\n  \"textAlign\": \"center\"\n});\n\nvar _StyledLink = Object(styled_components__WEBPACK_IMPORTED_MODULE_1__[/* default */ \"c\"])(react_router_dom__WEBPACK_IMPORTED_MODULE_3__[/* Link */ \"a\"]).withConfig({\n  displayName: \"LoginContainer___StyledLink\",\n  componentId: \"sc-qtrnpk-4\"\n})({\n  \"fontSize\": \"0.75rem\",\n  \"lineHeight\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(211, 12%, 43%, var(--tw-text-opacity))\",\n  \"letterSpacing\": \"0.025em\",\n  \"textDecoration\": \"none\",\n  \"textTransform\": \"uppercase\",\n  \":hover\": {\n    \"--tw-text-opacity\": \"1\",\n    \"color\": \"hsla(209, 14%, 37%, var(--tw-text-opacity))\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(LoginContainer, \"LoginContainer\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginContainer.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginContainer.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9jb21wb25lbnRzL2F1dGgvTG9naW5Db250YWluZXIudHN4PzA5Y2IiXSwibmFtZXMiOlsiTG9naW5Db250YWluZXIiLCJoaXN0b3J5IiwicmVmIiwidXNlUmVmIiwidG9rZW4iLCJzZXRUb2tlbiIsInVzZVN0YXRlIiwiY2xlYXJGbGFzaGVzIiwiY2xlYXJBbmRBZGRIdHRwRXJyb3IiLCJ1c2VGbGFzaCIsImVuYWJsZWQiLCJyZWNhcHRjaGFFbmFibGVkIiwic2l0ZUtleSIsInVzZVN0b3JlU3RhdGUiLCJzdGF0ZSIsInNldHRpbmdzIiwiZGF0YSIsInJlY2FwdGNoYSIsInVzZUVmZmVjdCIsIm9uU3VibWl0IiwidmFsdWVzIiwic2V0U3VibWl0dGluZyIsImN1cnJlbnQiLCJleGVjdXRlIiwiY2F0Y2giLCJlcnJvciIsImNvbnNvbGUiLCJsb2dpbiIsInJlY2FwdGNoYURhdGEiLCJ0aGVuIiwicmVzcG9uc2UiLCJjb21wbGV0ZSIsIndpbmRvdyIsImxvY2F0aW9uIiwiaW50ZW5kZWQiLCJyZXBsYWNlIiwiY29uZmlybWF0aW9uVG9rZW4iLCJyZXNldCIsInVzZXJuYW1lIiwicGFzc3dvcmQiLCJvYmplY3QiLCJzaGFwZSIsInN0cmluZyIsInJlcXVpcmVkIiwiaXNTdWJtaXR0aW5nIiwic3VibWl0Rm9ybSJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFFQTtBQUNBO0FBQ0E7O0FBT0EsTUFBTUEsY0FBYyxHQUFHLFFBQXNDO0VBQUEsSUFBckM7SUFBRUM7RUFBRixDQUFxQztFQUN6RCxNQUFNQyxHQUFHLEdBQUdDLG9EQUFNLENBQVcsSUFBWCxDQUFsQjtFQUNBLE1BQU0sQ0FBQ0MsS0FBRCxFQUFRQyxRQUFSLElBQW9CQyxzREFBUSxDQUFDLEVBQUQsQ0FBbEM7RUFFQSxNQUFNO0lBQUVDLFlBQUY7SUFBZ0JDO0VBQWhCLElBQXlDQywwRUFBUSxFQUF2RDtFQUNBLE1BQU07SUFBRUMsT0FBTyxFQUFFQyxnQkFBWDtJQUE2QkM7RUFBN0IsSUFBeUNDLGdFQUFhLENBQUVDLEtBQUQsSUFBV0EsS0FBSyxDQUFDQyxRQUFOLENBQWVDLElBQWYsQ0FBcUJDLFNBQWpDLENBQTVEO0VBRUFDLHVEQUFTLENBQUMsTUFBTTtJQUNaWCxZQUFZO0VBQ2YsQ0FGUSxFQUVOLEVBRk0sQ0FBVDs7RUFJQSxNQUFNWSxRQUFRLEdBQUcsQ0FBQ0MsTUFBRCxZQUE4RDtJQUFBLElBQTdDO01BQUVDO0lBQUYsQ0FBNkM7SUFDM0VkLFlBQVksR0FEK0QsQ0FHM0U7SUFDQTs7SUFDQSxJQUFJSSxnQkFBZ0IsSUFBSSxDQUFDUCxLQUF6QixFQUFnQztNQUM1QkYsR0FBRyxDQUFDb0IsT0FBSixDQUFhQyxPQUFiLEdBQXVCQyxLQUF2QixDQUE4QkMsS0FBRCxJQUFXO1FBQ3BDQyxPQUFPLENBQUNELEtBQVIsQ0FBY0EsS0FBZDtRQUVBSixhQUFhLENBQUMsS0FBRCxDQUFiO1FBQ0FiLG9CQUFvQixDQUFDO1VBQUVpQjtRQUFGLENBQUQsQ0FBcEI7TUFDSCxDQUxEO01BT0E7SUFDSDs7SUFFREUsdUVBQUssaUNBQU1QLE1BQU47TUFBY1EsYUFBYSxFQUFFeEI7SUFBN0IsR0FBTCxDQUNLeUIsSUFETCxDQUNXQyxRQUFELElBQWM7TUFDaEIsSUFBSUEsUUFBUSxDQUFDQyxRQUFiLEVBQXVCO1FBQ25CO1FBQ0FDLE1BQU0sQ0FBQ0MsUUFBUCxHQUFrQkgsUUFBUSxDQUFDSSxRQUFULElBQXFCLEdBQXZDO1FBQ0E7TUFDSDs7TUFFRGpDLE9BQU8sQ0FBQ2tDLE9BQVIsQ0FBZ0Isd0JBQWhCLEVBQTBDO1FBQUUvQixLQUFLLEVBQUUwQixRQUFRLENBQUNNO01BQWxCLENBQTFDO0lBQ0gsQ0FUTCxFQVVLWixLQVZMLENBVVlDLEtBQUQsSUFBVztNQUNkQyxPQUFPLENBQUNELEtBQVIsQ0FBY0EsS0FBZDtNQUVBcEIsUUFBUSxDQUFDLEVBQUQsQ0FBUjtNQUNBLElBQUlILEdBQUcsQ0FBQ29CLE9BQVIsRUFBaUJwQixHQUFHLENBQUNvQixPQUFKLENBQVllLEtBQVo7TUFFakJoQixhQUFhLENBQUMsS0FBRCxDQUFiO01BQ0FiLG9CQUFvQixDQUFDO1FBQUVpQjtNQUFGLENBQUQsQ0FBcEI7SUFDSCxDQWxCTDtFQW1CSCxDQW5DRDs7RUFxQ0Esb0JBQ0ksMkRBQUMscURBQUQ7SUFDSSxRQUFRLEVBQUVOLFFBRGQ7SUFFSSxhQUFhLEVBQUU7TUFBRW1CLFFBQVEsRUFBRSxFQUFaO01BQWdCQyxRQUFRLEVBQUU7SUFBMUIsQ0FGbkI7SUFHSSxnQkFBZ0IsRUFBRUMsMERBQU0sR0FBR0MsS0FBVCxDQUFlO01BQzdCSCxRQUFRLEVBQUVJLDBEQUFNLEdBQUdDLFFBQVQsQ0FBa0IsdUNBQWxCLENBRG1CO01BRTdCSixRQUFRLEVBQUVHLDBEQUFNLEdBQUdDLFFBQVQsQ0FBa0IscUNBQWxCO0lBRm1CLENBQWY7RUFIdEIsR0FRSztJQUFBLElBQUM7TUFBRUMsWUFBRjtNQUFnQnZCLGFBQWhCO01BQStCd0I7SUFBL0IsQ0FBRDtJQUFBLG9CQUNHO01BQW9CLEtBQUssRUFBRSxtQkFBM0I7TUFBQTtJQUFBLGdCQUNJLDJEQUFDLDBFQUFEO01BQU8sS0FBSyxNQUFaO01BQWEsSUFBSSxFQUFFLE1BQW5CO01BQTJCLEtBQUssRUFBRSxtQkFBbEM7TUFBdUQsSUFBSSxFQUFFLFVBQTdEO01BQXlFLFFBQVEsRUFBRUQ7SUFBbkYsRUFESixlQUVJO01BQUE7SUFBQSxnQkFDSSwyREFBQywwRUFBRDtNQUFPLEtBQUssTUFBWjtNQUFhLElBQUksRUFBRSxVQUFuQjtNQUErQixLQUFLLEVBQUUsVUFBdEM7TUFBa0QsSUFBSSxFQUFFLFVBQXhEO01BQW9FLFFBQVEsRUFBRUE7SUFBOUUsRUFESixDQUZKLGVBS0k7TUFBQTtJQUFBLGdCQUNJLDJEQUFDLDRFQUFEO01BQVEsSUFBSSxFQUFFLFFBQWQ7TUFBd0IsSUFBSSxFQUFFLFFBQTlCO01BQXdDLFNBQVMsRUFBRUEsWUFBbkQ7TUFBaUUsUUFBUSxFQUFFQTtJQUEzRSxXQURKLENBTEosRUFVS2pDLGdCQUFnQixpQkFDYiwyREFBQyxnREFBRDtNQUNJLEdBQUcsRUFBRVQsR0FEVDtNQUVJLElBQUksRUFBRSxXQUZWO01BR0ksT0FBTyxFQUFFVSxPQUFPLElBQUksY0FIeEI7TUFJSSxRQUFRLEVBQUdrQixRQUFELElBQWM7UUFDcEJ6QixRQUFRLENBQUN5QixRQUFELENBQVI7UUFDQWUsVUFBVTtNQUNiLENBUEw7TUFRSSxRQUFRLEVBQUUsTUFBTTtRQUNaeEIsYUFBYSxDQUFDLEtBQUQsQ0FBYjtRQUNBaEIsUUFBUSxDQUFDLEVBQUQsQ0FBUjtNQUNIO0lBWEwsRUFYUixlQXlCSTtNQUFBO0lBQUEsZ0JBQ0k7TUFDSSxFQUFFLEVBQUUsZ0JBRFI7TUFBQTtJQUFBLHNCQURKLENBekJKLENBREg7RUFBQSxDQVJMLENBREo7QUErQ0gsQ0EvRkQ7O2NBQU1MLGMsK0tBSTZDUyxrRSxFQUNBSSx3RDs7aUJBNEZwQ2IsYztBQUFBOzs7OztHQXZDd0Q7RUFBQTtFQUFBO0FBQUEsQzs7Ozs7R0FFdkM7RUFBQTtBQUFBLEM7Ozs7O0dBR0E7RUFBQTtBQUFBLEM7Ozs7O0dBb0JBO0VBQUE7RUFBQTtBQUFBLEM7Ozs7O0dBR0c7RUFBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0VBQUE7RUFBQTtFQUFBO0lBQUE7SUFBQTtFQUFBO0FBQUEsQzs7Ozs7Ozs7Ozs7MEJBdEY3QkEsYyIsImZpbGUiOiIuL3Jlc291cmNlcy9zY3JpcHRzL2NvbXBvbmVudHMvYXV0aC9Mb2dpbkNvbnRhaW5lci50c3guanMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgTGluaywgUm91dGVDb21wb25lbnRQcm9wcyB9IGZyb20gJ3JlYWN0LXJvdXRlci1kb20nO1xuaW1wb3J0IGxvZ2luIGZyb20gJ0AvYXBpL2F1dGgvbG9naW4nO1xuaW1wb3J0IExvZ2luRm9ybUNvbnRhaW5lciBmcm9tICdAL2NvbXBvbmVudHMvYXV0aC9Mb2dpbkZvcm1Db250YWluZXInO1xuaW1wb3J0IHsgdXNlU3RvcmVTdGF0ZSB9IGZyb20gJ2Vhc3ktcGVhc3knO1xuaW1wb3J0IHsgRm9ybWlrLCBGb3JtaWtIZWxwZXJzIH0gZnJvbSAnZm9ybWlrJztcbmltcG9ydCB7IG9iamVjdCwgc3RyaW5nIH0gZnJvbSAneXVwJztcbmltcG9ydCBGaWVsZCBmcm9tICdAL2NvbXBvbmVudHMvZWxlbWVudHMvRmllbGQnO1xuaW1wb3J0IHR3IGZyb20gJ3R3aW4ubWFjcm8nO1xuaW1wb3J0IEJ1dHRvbiBmcm9tICdAL2NvbXBvbmVudHMvZWxlbWVudHMvQnV0dG9uJztcbmltcG9ydCBSZWFwdGNoYSBmcm9tICdyZWFwdGNoYSc7XG5pbXBvcnQgdXNlRmxhc2ggZnJvbSAnQC9wbHVnaW5zL3VzZUZsYXNoJztcblxuaW50ZXJmYWNlIFZhbHVlcyB7XG4gICAgdXNlcm5hbWU6IHN0cmluZztcbiAgICBwYXNzd29yZDogc3RyaW5nO1xufVxuXG5jb25zdCBMb2dpbkNvbnRhaW5lciA9ICh7IGhpc3RvcnkgfTogUm91dGVDb21wb25lbnRQcm9wcykgPT4ge1xuICAgIGNvbnN0IHJlZiA9IHVzZVJlZjxSZWFwdGNoYT4obnVsbCk7XG4gICAgY29uc3QgW3Rva2VuLCBzZXRUb2tlbl0gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgICBjb25zdCB7IGNsZWFyRmxhc2hlcywgY2xlYXJBbmRBZGRIdHRwRXJyb3IgfSA9IHVzZUZsYXNoKCk7XG4gICAgY29uc3QgeyBlbmFibGVkOiByZWNhcHRjaGFFbmFibGVkLCBzaXRlS2V5IH0gPSB1c2VTdG9yZVN0YXRlKChzdGF0ZSkgPT4gc3RhdGUuc2V0dGluZ3MuZGF0YSEucmVjYXB0Y2hhKTtcblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNsZWFyRmxhc2hlcygpO1xuICAgIH0sIFtdKTtcblxuICAgIGNvbnN0IG9uU3VibWl0ID0gKHZhbHVlczogVmFsdWVzLCB7IHNldFN1Ym1pdHRpbmcgfTogRm9ybWlrSGVscGVyczxWYWx1ZXM+KSA9PiB7XG4gICAgICAgIGNsZWFyRmxhc2hlcygpO1xuXG4gICAgICAgIC8vIElmIHRoZXJlIGlzIG5vIHRva2VuIGluIHRoZSBzdGF0ZSB5ZXQsIHJlcXVlc3QgdGhlIHRva2VuIGFuZCB0aGVuIGFib3J0IHRoaXMgc3VibWl0IHJlcXVlc3RcbiAgICAgICAgLy8gc2luY2UgaXQgd2lsbCBiZSByZS1zdWJtaXR0ZWQgd2hlbiB0aGUgcmVjYXB0Y2hhIGRhdGEgaXMgcmV0dXJuZWQgYnkgdGhlIGNvbXBvbmVudC5cbiAgICAgICAgaWYgKHJlY2FwdGNoYUVuYWJsZWQgJiYgIXRva2VuKSB7XG4gICAgICAgICAgICByZWYuY3VycmVudCEuZXhlY3V0ZSgpLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xuXG4gICAgICAgICAgICAgICAgc2V0U3VibWl0dGluZyhmYWxzZSk7XG4gICAgICAgICAgICAgICAgY2xlYXJBbmRBZGRIdHRwRXJyb3IoeyBlcnJvciB9KTtcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICBsb2dpbih7IC4uLnZhbHVlcywgcmVjYXB0Y2hhRGF0YTogdG9rZW4gfSlcbiAgICAgICAgICAgIC50aGVuKChyZXNwb25zZSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5jb21wbGV0ZSkge1xuICAgICAgICAgICAgICAgICAgICAvLyBAdHMtZXhwZWN0LWVycm9yIHRoaXMgaXMgdmFsaWRcbiAgICAgICAgICAgICAgICAgICAgd2luZG93LmxvY2F0aW9uID0gcmVzcG9uc2UuaW50ZW5kZWQgfHwgJy8nO1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgaGlzdG9yeS5yZXBsYWNlKCcvYXV0aC9sb2dpbi9jaGVja3BvaW50JywgeyB0b2tlbjogcmVzcG9uc2UuY29uZmlybWF0aW9uVG9rZW4gfSk7XG4gICAgICAgICAgICB9KVxuICAgICAgICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoZXJyb3IpO1xuXG4gICAgICAgICAgICAgICAgc2V0VG9rZW4oJycpO1xuICAgICAgICAgICAgICAgIGlmIChyZWYuY3VycmVudCkgcmVmLmN1cnJlbnQucmVzZXQoKTtcblxuICAgICAgICAgICAgICAgIHNldFN1Ym1pdHRpbmcoZmFsc2UpO1xuICAgICAgICAgICAgICAgIGNsZWFyQW5kQWRkSHR0cEVycm9yKHsgZXJyb3IgfSk7XG4gICAgICAgICAgICB9KTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIChcbiAgICAgICAgPEZvcm1pa1xuICAgICAgICAgICAgb25TdWJtaXQ9e29uU3VibWl0fVxuICAgICAgICAgICAgaW5pdGlhbFZhbHVlcz17eyB1c2VybmFtZTogJycsIHBhc3N3b3JkOiAnJyB9fVxuICAgICAgICAgICAgdmFsaWRhdGlvblNjaGVtYT17b2JqZWN0KCkuc2hhcGUoe1xuICAgICAgICAgICAgICAgIHVzZXJuYW1lOiBzdHJpbmcoKS5yZXF1aXJlZCgnQSB1c2VybmFtZSBvciBlbWFpbCBtdXN0IGJlIHByb3ZpZGVkLicpLFxuICAgICAgICAgICAgICAgIHBhc3N3b3JkOiBzdHJpbmcoKS5yZXF1aXJlZCgnUGxlYXNlIGVudGVyIHlvdXIgYWNjb3VudCBwYXNzd29yZC4nKSxcbiAgICAgICAgICAgIH0pfVxuICAgICAgICA+XG4gICAgICAgICAgICB7KHsgaXNTdWJtaXR0aW5nLCBzZXRTdWJtaXR0aW5nLCBzdWJtaXRGb3JtIH0pID0+IChcbiAgICAgICAgICAgICAgICA8TG9naW5Gb3JtQ29udGFpbmVyIHRpdGxlPXsnTG9naW4gdG8gQ29udGludWUnfSBjc3M9e3R3YHctZnVsbCBmbGV4YH0+XG4gICAgICAgICAgICAgICAgICAgIDxGaWVsZCBsaWdodCB0eXBlPXsndGV4dCd9IGxhYmVsPXsnVXNlcm5hbWUgb3IgRW1haWwnfSBuYW1lPXsndXNlcm5hbWUnfSBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfSAvPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNzcz17dHdgbXQtNmB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZpZWxkIGxpZ2h0IHR5cGU9eydwYXNzd29yZCd9IGxhYmVsPXsnUGFzc3dvcmQnfSBuYW1lPXsncGFzc3dvcmQnfSBkaXNhYmxlZD17aXNTdWJtaXR0aW5nfSAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjc3M9e3R3YG10LTZgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gdHlwZT17J3N1Ym1pdCd9IHNpemU9eyd4bGFyZ2UnfSBpc0xvYWRpbmc9e2lzU3VibWl0dGluZ30gZGlzYWJsZWQ9e2lzU3VibWl0dGluZ30+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgTG9naW5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAge3JlY2FwdGNoYUVuYWJsZWQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPFJlYXB0Y2hhXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT17J2ludmlzaWJsZSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l0ZWtleT17c2l0ZUtleSB8fCAnX2ludmFsaWRfa2V5J31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBvblZlcmlmeT17KHJlc3BvbnNlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRva2VuKHJlc3BvbnNlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc3VibWl0Rm9ybSgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgb25FeHBpcmU9eygpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2V0U3VibWl0dGluZyhmYWxzZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNldFRva2VuKCcnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjc3M9e3R3YG10LTYgdGV4dC1jZW50ZXJgfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdG89eycvYXV0aC9wYXNzd29yZCd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgY3NzPXt0d2B0ZXh0LXhzIHRleHQtbmV1dHJhbC01MDAgdHJhY2tpbmctd2lkZSBuby11bmRlcmxpbmUgdXBwZXJjYXNlIGhvdmVyOnRleHQtbmV1dHJhbC02MDBgfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIEZvcmdvdCBwYXNzd29yZD9cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9Mb2dpbkZvcm1Db250YWluZXI+XG4gICAgICAgICAgICApfVxuICAgICAgICA8L0Zvcm1paz5cbiAgICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTG9naW5Db250YWluZXI7XG4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./resources/scripts/components/auth/LoginContainer.tsx\n");

/***/ }),

/***/ "./resources/scripts/components/auth/LoginFormContainer.tsx":
/*!******************************************************************!*\
  !*** ./resources/scripts/components/auth/LoginFormContainer.tsx ***!
  \******************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ \"./node_modules/@babel/runtime/helpers/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/objectWithoutProperties */ \"./node_modules/@babel/runtime/helpers/objectWithoutProperties.js\");\n/* harmony import */ var _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @babel/runtime/helpers/taggedTemplateLiteral */ \"./node_modules/@babel/runtime/helpers/taggedTemplateLiteral.js\");\n/* harmony import */ var _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! formik */ \"./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _theme__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/theme */ \"./resources/scripts/theme.ts\");\n/* harmony import */ var _components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/FlashMessageRender */ \"./resources/scripts/components/FlashMessageRender.tsx\");\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nfunction _templateObject4() {\n  const data = _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2___default()([\"\\n        \", \"\\n        max-width: 700px;\\n    \"]);\n\n  _templateObject4 = function () {\n    return data;\n  };\n\n  return data;\n}\n\nfunction _templateObject3() {\n  const data = _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2___default()([\"\\n        \", \"\\n    \"]);\n\n  _templateObject3 = function () {\n    return data;\n  };\n\n  return data;\n}\n\nfunction _templateObject2() {\n  const data = _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2___default()([\"\\n        \", \"\\n    \"]);\n\n  _templateObject2 = function () {\n    return data;\n  };\n\n  return data;\n}\n\nfunction _templateObject() {\n  const data = _babel_runtime_helpers_taggedTemplateLiteral__WEBPACK_IMPORTED_MODULE_2___default()([\"\\n        \", \"\\n    \"]);\n\n  _templateObject = function () {\n    return data;\n  };\n\n  return data;\n}\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\nconst Container = /*#__PURE__*/styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"].div.withConfig({\n  displayName: \"LoginFormContainer__Container\",\n  componentId: \"sc-cyh04c-0\"\n})([\"\", \";\", \";\", \";\", \";\"], Object(_theme__WEBPACK_IMPORTED_MODULE_6__[/* breakpoint */ \"a\"])('sm')(_templateObject(), {\n  \"width\": \"80%\",\n  \"marginLeft\": \"auto\",\n  \"marginRight\": \"auto\"\n}), Object(_theme__WEBPACK_IMPORTED_MODULE_6__[/* breakpoint */ \"a\"])('md')(_templateObject2(), {\n  \"padding\": \"2.5rem\"\n}), Object(_theme__WEBPACK_IMPORTED_MODULE_6__[/* breakpoint */ \"a\"])('lg')(_templateObject3(), {\n  \"width\": \"60%\"\n}), Object(_theme__WEBPACK_IMPORTED_MODULE_6__[/* breakpoint */ \"a\"])('xl')(_templateObject4(), {\n  \"width\": \"100%\"\n}));\n\nconst _default = /*#__PURE__*/Object(react__WEBPACK_IMPORTED_MODULE_4__[\"forwardRef\"])((_ref, ref) => {\n  let {\n    title\n  } = _ref,\n      props = _babel_runtime_helpers_objectWithoutProperties__WEBPACK_IMPORTED_MODULE_1___default()(_ref, [\"title\"]);\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(Container, null, title && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(_StyledH, {\n    $_css: {\n      \"fontSize\": \"1.875rem\",\n      \"lineHeight\": \"2.25rem\",\n      \"textAlign\": \"center\",\n      \"--tw-text-opacity\": \"1\",\n      \"color\": \"hsla(214, 15%, 91%, var(--tw-text-opacity))\",\n      \"fontWeight\": \"500\",\n      \"paddingTop\": \"1rem\",\n      \"paddingBottom\": \"1rem\"\n    },\n    \"data-tw\": \"text-3xl text-center text-neutral-100 font-medium py-4\"\n  }, title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(_StyledFlashMessageRender, {\n    $_css2: {\n      \"marginBottom\": \"0.5rem\",\n      \"paddingLeft\": \"0.25rem\",\n      \"paddingRight\": \"0.25rem\"\n    },\n    \"data-tw\": \"mb-2 px-1\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(formik__WEBPACK_IMPORTED_MODULE_5__[/* Form */ \"b\"], _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0___default()({}, props, {\n    ref: ref\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(_StyledDiv, {\n    $_css3: {\n      \"width\": \"100%\",\n      \"--tw-bg-opacity\": \"1\",\n      \"backgroundColor\": \"rgba(255, 255, 255, var(--tw-bg-opacity))\",\n      \"--tw-shadow\": \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\",\n      \"boxShadow\": \"var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)\",\n      \"borderRadius\": \"0.5rem\",\n      \"padding\": \"1.5rem\",\n      \"marginLeft\": \"0.25rem\",\n      \"marginRight\": \"0.25rem\",\n      \"@media (min-width: 768px)\": {\n        \"display\": \"flex\",\n        \"paddingLeft\": \"0px\"\n      }\n    },\n    \"data-tw\": \"md:flex w-full bg-white shadow-lg rounded-lg p-6 md:pl-0 mx-1\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(_StyledDiv2, {\n    $_css4: {\n      \"flex\": \"none\",\n      \"userSelect\": \"none\",\n      \"marginBottom\": \"1.5rem\",\n      \"alignSelf\": \"center\",\n      \"@media (min-width: 768px)\": {\n        \"marginBottom\": \"0px\"\n      }\n    },\n    \"data-tw\": \"flex-none select-none mb-6 md:mb-0 self-center\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(_StyledImg, {\n    src: '/assets/svgs/pterodactyl.svg',\n    $_css5: {\n      \"display\": \"block\",\n      \"width\": \"12rem\",\n      \"marginLeft\": \"auto\",\n      \"marginRight\": \"auto\",\n      \"@media (min-width: 768px)\": {\n        \"width\": \"16rem\"\n      }\n    },\n    \"data-tw\": \"block w-48 md:w-64 mx-auto\"\n  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(_StyledDiv3, {\n    $_css6: {\n      \"flex\": \"1 1 0%\"\n    },\n    \"data-tw\": \"flex-1\"\n  }, props.children))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(_StyledP, {\n    $_css7: {\n      \"textAlign\": \"center\",\n      \"--tw-text-opacity\": \"1\",\n      \"color\": \"hsla(211, 12%, 43%, var(--tw-text-opacity))\",\n      \"fontSize\": \"0.75rem\",\n      \"lineHeight\": \"1rem\",\n      \"marginTop\": \"1rem\"\n    },\n    \"data-tw\": \"text-center text-neutral-500 text-xs mt-4\"\n  }, \"\\xA9 2015 - \", new Date().getFullYear(), \"\\xA0\", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_4___default.a.createElement(_StyledA, {\n    rel: 'noopener nofollow noreferrer',\n    href: 'https://pterodactyl.io',\n    target: '_blank',\n    $_css8: {\n      \"textDecoration\": \"none\",\n      \"--tw-text-opacity\": \"1\",\n      \"color\": \"hsla(211, 12%, 43%, var(--tw-text-opacity))\",\n      \":hover\": {\n        \"--tw-text-opacity\": \"1\",\n        \"color\": \"hsla(211, 13%, 65%, var(--tw-text-opacity))\"\n      }\n    },\n    \"data-tw\": \"no-underline text-neutral-500 hover:text-neutral-300\"\n  }, \"Pterodactyl Software\")));\n});\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n\nvar _StyledH = /*#__PURE__*/Object(styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"])(\"h2\").withConfig({\n  displayName: \"LoginFormContainer___StyledH\",\n  componentId: \"sc-cyh04c-1\"\n})([\"\", \"\"], p => p.$_css);\n\nvar _StyledFlashMessageRender = /*#__PURE__*/Object(styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"])(_components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_7__[/* default */ \"a\"]).withConfig({\n  displayName: \"LoginFormContainer___StyledFlashMessageRender\",\n  componentId: \"sc-cyh04c-2\"\n})([\"\", \"\"], p => p.$_css2);\n\nvar _StyledDiv = /*#__PURE__*/Object(styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginFormContainer___StyledDiv\",\n  componentId: \"sc-cyh04c-3\"\n})([\"\", \"\"], p => p.$_css3);\n\nvar _StyledDiv2 = /*#__PURE__*/Object(styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginFormContainer___StyledDiv2\",\n  componentId: \"sc-cyh04c-4\"\n})([\"\", \"\"], p => p.$_css4);\n\nvar _StyledImg = /*#__PURE__*/Object(styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"])(\"img\").withConfig({\n  displayName: \"LoginFormContainer___StyledImg\",\n  componentId: \"sc-cyh04c-5\"\n})([\"\", \"\"], p => p.$_css5);\n\nvar _StyledDiv3 = /*#__PURE__*/Object(styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"LoginFormContainer___StyledDiv3\",\n  componentId: \"sc-cyh04c-6\"\n})([\"\", \"\"], p => p.$_css6);\n\nvar _StyledP = /*#__PURE__*/Object(styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"LoginFormContainer___StyledP\",\n  componentId: \"sc-cyh04c-7\"\n})([\"\", \"\"], p => p.$_css7);\n\nvar _StyledA = /*#__PURE__*/Object(styled_components__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"c\"])(\"a\").withConfig({\n  displayName: \"LoginFormContainer___StyledA\",\n  componentId: \"sc-cyh04c-8\"\n})([\"\", \"\"], p => p.$_css8);\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(Container, \"Container\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_StyledH, \"_StyledH\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_StyledFlashMessageRender, \"_StyledFlashMessageRender\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_StyledDiv, \"_StyledDiv\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_StyledDiv2, \"_StyledDiv2\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_StyledImg, \"_StyledImg\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_StyledDiv3, \"_StyledDiv3\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_StyledP, \"_StyledP\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_StyledA, \"_StyledA\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/auth/LoginFormContainer.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/auth/LoginFormContainer.tsx\n");

/***/ }),

/***/ "./resources/scripts/components/auth/ResetPasswordContainer.tsx":
/*!**********************************************************************!*\
  !*** ./resources/scripts/components/auth/ResetPasswordContainer.tsx ***!
  \**********************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-router-dom */ \"./node_modules/react-router-dom/esm/react-router-dom.js\");\n/* harmony import */ var _api_auth_performPasswordReset__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/auth/performPasswordReset */ \"./resources/scripts/api/auth/performPasswordReset.ts\");\n/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n/* harmony import */ var _components_auth_LoginFormContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/LoginFormContainer */ \"./resources/scripts/components/auth/LoginFormContainer.tsx\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! easy-peasy */ \"./node_modules/easy-peasy/dist/easy-peasy.js\");\n/* harmony import */ var easy_peasy__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(easy_peasy__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! formik */ \"./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! yup */ \"./node_modules/yup/es/index.js\");\n/* harmony import */ var _components_elements_Field__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/elements/Field */ \"./resources/scripts/components/elements/Field.tsx\");\n/* harmony import */ var _components_elements_Input__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/elements/Input */ \"./resources/scripts/components/elements/Input.tsx\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n\n\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst _default = _ref => {\n  let {\n    match,\n    location\n  } = _ref;\n  const [email, setEmail] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])('');\n  const {\n    clearFlashes,\n    addFlash\n  } = Object(easy_peasy__WEBPACK_IMPORTED_MODULE_6__[\"useStoreActions\"])(actions => actions.flashes);\n  const parsed = new URLSearchParams(location.search);\n\n  if (email.length === 0 && parsed.get('email')) {\n    setEmail(parsed.get('email') || '');\n  }\n\n  const submit = (_ref2, _ref3) => {\n    let {\n      password,\n      passwordConfirmation\n    } = _ref2;\n    let {\n      setSubmitting\n    } = _ref3;\n    clearFlashes();\n    Object(_api_auth_performPasswordReset__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"a\"])(email, {\n      token: match.params.token,\n      password,\n      passwordConfirmation\n    }).then(() => {\n      // @ts-expect-error this is valid\n      window.location = '/';\n    }).catch(error => {\n      console.error(error);\n      setSubmitting(false);\n      addFlash({\n        type: 'error',\n        title: 'Error',\n        message: Object(_api_http__WEBPACK_IMPORTED_MODULE_4__[/* httpErrorToHuman */ \"c\"])(error)\n      });\n    });\n  };\n\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(formik__WEBPACK_IMPORTED_MODULE_7__[/* Formik */ \"c\"], {\n    onSubmit: submit,\n    initialValues: {\n      password: '',\n      passwordConfirmation: ''\n    },\n    validationSchema: Object(yup__WEBPACK_IMPORTED_MODULE_8__[/* object */ \"d\"])().shape({\n      password: Object(yup__WEBPACK_IMPORTED_MODULE_8__[/* string */ \"f\"])().required('A new password is required.').min(8, 'Your new password should be at least 8 characters in length.'),\n      passwordConfirmation: Object(yup__WEBPACK_IMPORTED_MODULE_8__[/* string */ \"f\"])().required('Your new password does not match.') // @ts-expect-error this is valid\n      .oneOf([Object(yup__WEBPACK_IMPORTED_MODULE_8__[/* ref */ \"e\"])('password'), null], 'Your new password does not match.')\n    })\n  }, _ref4 => {\n    let {\n      isSubmitting\n    } = _ref4;\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledLoginFormContainer, {\n      title: 'Reset Password',\n      \"data-tw\": \"w-full flex\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(\"div\", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(\"label\", null, \"Email\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Input__WEBPACK_IMPORTED_MODULE_10__[/* default */ \"b\"], {\n      value: email,\n      isLight: true,\n      disabled: true\n    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv, {\n      \"data-tw\": \"mt-6\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Field__WEBPACK_IMPORTED_MODULE_9__[/* default */ \"a\"], {\n      light: true,\n      label: 'New Password',\n      name: 'password',\n      type: 'password',\n      description: 'Passwords must be at least 8 characters in length.'\n    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv2, {\n      \"data-tw\": \"mt-6\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Field__WEBPACK_IMPORTED_MODULE_9__[/* default */ \"a\"], {\n      light: true,\n      label: 'Confirm New Password',\n      name: 'passwordConfirmation',\n      type: 'password'\n    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv3, {\n      \"data-tw\": \"mt-6\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Button__WEBPACK_IMPORTED_MODULE_11__[/* default */ \"a\"], {\n      size: 'xlarge',\n      type: 'submit',\n      disabled: isSubmitting,\n      isLoading: isSubmitting\n    }, \"Reset Password\")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv4, {\n      \"data-tw\": \"mt-6 text-center\"\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledLink, {\n      to: '/auth/login',\n      \"data-tw\": \"text-xs text-neutral-500 tracking-wide no-underline uppercase hover:text-neutral-600\"\n    }, \"Return to Login\")));\n  });\n};\n\n__signature__(_default, \"useState{[email, setEmail]('')}\\nuseStoreActions{{ clearFlashes, addFlash }}\", () => [easy_peasy__WEBPACK_IMPORTED_MODULE_6__[\"useStoreActions\"]]);\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n\nvar _StyledLoginFormContainer = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_auth_LoginFormContainer__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"]).withConfig({\n  displayName: \"ResetPasswordContainer___StyledLoginFormContainer\",\n  componentId: \"sc-1gvxg4x-0\"\n})({\n  \"width\": \"100%\",\n  \"display\": \"flex\"\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"ResetPasswordContainer___StyledDiv\",\n  componentId: \"sc-1gvxg4x-1\"\n})({\n  \"marginTop\": \"1.5rem\"\n});\n\nvar _StyledDiv2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"ResetPasswordContainer___StyledDiv2\",\n  componentId: \"sc-1gvxg4x-2\"\n})({\n  \"marginTop\": \"1.5rem\"\n});\n\nvar _StyledDiv3 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"ResetPasswordContainer___StyledDiv3\",\n  componentId: \"sc-1gvxg4x-3\"\n})({\n  \"marginTop\": \"1.5rem\"\n});\n\nvar _StyledDiv4 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"ResetPasswordContainer___StyledDiv4\",\n  componentId: \"sc-1gvxg4x-4\"\n})({\n  \"marginTop\": \"1.5rem\",\n  \"textAlign\": \"center\"\n});\n\nvar _StyledLink = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(react_router_dom__WEBPACK_IMPORTED_MODULE_2__[/* Link */ \"a\"]).withConfig({\n  displayName: \"ResetPasswordContainer___StyledLink\",\n  componentId: \"sc-1gvxg4x-5\"\n})({\n  \"fontSize\": \"0.75rem\",\n  \"lineHeight\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(211, 12%, 43%, var(--tw-text-opacity))\",\n  \"letterSpacing\": \"0.025em\",\n  \"textDecoration\": \"none\",\n  \"textTransform\": \"uppercase\",\n  \":hover\": {\n    \"--tw-text-opacity\": \"1\",\n    \"color\": \"hsla(209, 14%, 37%, var(--tw-text-opacity))\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/auth/ResetPasswordContainer.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/auth/ResetPasswordContainer.tsx\n");

/***/ }),

/***/ "./resources/scripts/routers/AuthenticationRouter.tsx":
/*!************************************************************!*\
  !*** ./resources/scripts/routers/AuthenticationRouter.tsx ***!
  \************************************************************/
/*! exports provided: default */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_router_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-router-dom */ \"./node_modules/react-router-dom/esm/react-router-dom.js\");\n/* harmony import */ var _components_auth_LoginContainer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/auth/LoginContainer */ \"./resources/scripts/components/auth/LoginContainer.tsx\");\n/* harmony import */ var _components_auth_ForgotPasswordContainer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/auth/ForgotPasswordContainer */ \"./resources/scripts/components/auth/ForgotPasswordContainer.tsx\");\n/* harmony import */ var _components_auth_ResetPasswordContainer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/auth/ResetPasswordContainer */ \"./resources/scripts/components/auth/ResetPasswordContainer.tsx\");\n/* harmony import */ var _components_auth_LoginCheckpointContainer__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/auth/LoginCheckpointContainer */ \"./resources/scripts/components/auth/LoginCheckpointContainer.tsx\");\n/* harmony import */ var _components_elements_ScreenBlock__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/elements/ScreenBlock */ \"./resources/scripts/components/elements/ScreenBlock.tsx\");\n/* harmony import */ var react_router__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-router */ \"./node_modules/react-router/esm/react-router.js\");\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\nconst _default = () => {\n  const history = Object(react_router__WEBPACK_IMPORTED_MODULE_7__[/* useHistory */ \"g\"])();\n  const location = Object(react_router__WEBPACK_IMPORTED_MODULE_7__[/* useLocation */ \"h\"])();\n  const {\n    path\n  } = Object(react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* useRouteMatch */ \"i\"])();\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(\"div\", {\n    className: 'pt-8 xl:pt-32'\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* Switch */ \"e\"], {\n    location: location\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* Route */ \"c\"], {\n    path: \"\".concat(path, \"/login\"),\n    component: _components_auth_LoginContainer__WEBPACK_IMPORTED_MODULE_2__[/* default */ \"a\"],\n    exact: true\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* Route */ \"c\"], {\n    path: \"\".concat(path, \"/login/checkpoint\"),\n    component: _components_auth_LoginCheckpointContainer__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"]\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* Route */ \"c\"], {\n    path: \"\".concat(path, \"/password\"),\n    component: _components_auth_ForgotPasswordContainer__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"a\"],\n    exact: true\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* Route */ \"c\"], {\n    path: \"\".concat(path, \"/password/reset/:token\"),\n    component: _components_auth_ResetPasswordContainer__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"]\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* Route */ \"c\"], {\n    path: \"\".concat(path, \"/checkpoint\")\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* Route */ \"c\"], {\n    path: '*'\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default.a.createElement(_components_elements_ScreenBlock__WEBPACK_IMPORTED_MODULE_6__[/* NotFound */ \"a\"], {\n    onBack: () => history.push('/auth/login')\n  }))));\n};\n\n__signature__(_default, \"useHistory{history}\\nuseLocation{location}\\nuseRouteMatch{{ path }}\", () => [react_router__WEBPACK_IMPORTED_MODULE_7__[/* useHistory */ \"g\"], react_router__WEBPACK_IMPORTED_MODULE_7__[/* useLocation */ \"h\"], react_router_dom__WEBPACK_IMPORTED_MODULE_1__[/* useRouteMatch */ \"i\"]]);\n\n/* harmony default export */ __webpack_exports__[\"default\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/routers/AuthenticationRouter.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/routers/AuthenticationRouter.tsx\n");

/***/ })

}]);