(window["webpackJsonp"] = window["webpackJsonp"] || []).push([[4],{

/***/ "./resources/scripts/api/server/files/saveFileContents.ts":
/*!****************************************************************!*\
  !*** ./resources/scripts/api/server/files/saveFileContents.ts ***!
  \****************************************************************/
/*! exports provided: default */
/*! exports used: default */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var _api_http__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/http */ \"./resources/scripts/api/http.ts\");\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\nconst _default = async (uuid, file, content) => {\n  await _api_http__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"a\"].post(\"/api/client/servers/\".concat(uuid, \"/files/write\"), content, {\n    params: {\n      file\n    },\n    headers: {\n      'Content-Type': 'text/plain'\n    }\n  });\n};\n\n/* harmony default export */ __webpack_exports__[\"a\"] = (_default);\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/api/server/files/saveFileContents.ts\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8vLi9yZXNvdXJjZXMvc2NyaXB0cy9hcGkvc2VydmVyL2ZpbGVzL3NhdmVGaWxlQ29udGVudHMudHM/ODdlMSJdLCJuYW1lcyI6WyJ1dWlkIiwiZmlsZSIsImNvbnRlbnQiLCJodHRwIiwicG9zdCIsInBhcmFtcyIsImhlYWRlcnMiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7aUJBRWUsT0FBT0EsSUFBUCxFQUFxQkMsSUFBckIsRUFBbUNDLE9BQW5DLEtBQXNFO0VBQ2pGLE1BQU1DLHlEQUFJLENBQUNDLElBQUwsK0JBQWlDSixJQUFqQyxtQkFBcURFLE9BQXJELEVBQThEO0lBQ2hFRyxNQUFNLEVBQUU7TUFBRUo7SUFBRixDQUR3RDtJQUVoRUssT0FBTyxFQUFFO01BQ0wsZ0JBQWdCO0lBRFg7RUFGdUQsQ0FBOUQsQ0FBTjtBQU1ILEM7O0FBUGMiLCJmaWxlIjoiLi9yZXNvdXJjZXMvc2NyaXB0cy9hcGkvc2VydmVyL2ZpbGVzL3NhdmVGaWxlQ29udGVudHMudHMuanMiLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgaHR0cCBmcm9tICdAL2FwaS9odHRwJztcblxuZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHV1aWQ6IHN0cmluZywgZmlsZTogc3RyaW5nLCBjb250ZW50OiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBhd2FpdCBodHRwLnBvc3QoYC9hcGkvY2xpZW50L3NlcnZlcnMvJHt1dWlkfS9maWxlcy93cml0ZWAsIGNvbnRlbnQsIHtcbiAgICAgICAgcGFyYW1zOiB7IGZpbGUgfSxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICd0ZXh0L3BsYWluJyxcbiAgICAgICAgfSxcbiAgICB9KTtcbn07XG4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./resources/scripts/api/server/files/saveFileContents.ts\n");

/***/ }),

/***/ "./resources/scripts/components/server/features/eula/EulaModalFeature.tsx":
/*!********************************************************************************!*\
  !*** ./resources/scripts/components/server/features/eula/EulaModalFeature.tsx ***!
  \********************************************************************************/
/*! exports provided: default */
/*! all exports used */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* WEBPACK VAR INJECTION */(function(module) {/* harmony import */ var styled_components__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! styled-components */ \"./node_modules/styled-components/dist/styled-components.browser.esm.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _state_server__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/state/server */ \"./resources/scripts/state/server/index.ts\");\n/* harmony import */ var _components_elements_Modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/elements/Modal */ \"./resources/scripts/components/elements/Modal.tsx\");\n/* harmony import */ var _components_elements_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/elements/Button */ \"./resources/scripts/components/elements/Button.tsx\");\n/* harmony import */ var _api_server_files_saveFileContents__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/api/server/files/saveFileContents */ \"./resources/scripts/api/server/files/saveFileContents.ts\");\n/* harmony import */ var _components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/FlashMessageRender */ \"./resources/scripts/components/FlashMessageRender.tsx\");\n/* harmony import */ var _plugins_useFlash__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/plugins/useFlash */ \"./resources/scripts/plugins/useFlash.ts\");\n/* harmony import */ var _components_server_events__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/server/events */ \"./resources/scripts/components/server/events.ts\");\n\n\n\n\n\n\n\n\n(function () {\n  var enterModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.enterModule : undefined;\n  enterModule && enterModule(module);\n})();\n\nvar __signature__ = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default.signature : function (a) {\n  return a;\n};\n\n\n\n\n\n\n\n\n\n\nconst EulaModalFeature = () => {\n  const [visible, setVisible] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const [loading, setLoading] = Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useState\"])(false);\n  const uuid = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.server.data.uuid);\n  const status = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.status.value);\n  const {\n    clearFlashes,\n    clearAndAddHttpError\n  } = Object(_plugins_useFlash__WEBPACK_IMPORTED_MODULE_7__[/* default */ \"a\"])();\n  const {\n    connected,\n    instance\n  } = _state_server__WEBPACK_IMPORTED_MODULE_2__[/* ServerContext */ \"a\"].useStoreState(state => state.socket);\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    if (!connected || !instance || status === 'running') return;\n\n    const listener = line => {\n      if (line.toLowerCase().indexOf('you need to agree to the eula in order to run the server') >= 0) {\n        setVisible(true);\n      }\n    };\n\n    instance.addListener(_components_server_events__WEBPACK_IMPORTED_MODULE_8__[/* SocketEvent */ \"a\"].CONSOLE_OUTPUT, listener);\n    return () => {\n      instance.removeListener(_components_server_events__WEBPACK_IMPORTED_MODULE_8__[/* SocketEvent */ \"a\"].CONSOLE_OUTPUT, listener);\n    };\n  }, [connected, instance, status]);\n\n  const onAcceptEULA = () => {\n    setLoading(true);\n    clearFlashes('feature:eula');\n    Object(_api_server_files_saveFileContents__WEBPACK_IMPORTED_MODULE_5__[/* default */ \"a\"])(uuid, 'eula.txt', 'eula=true').then(() => {\n      if (status === 'offline' && instance) {\n        instance.send(_components_server_events__WEBPACK_IMPORTED_MODULE_8__[/* SocketRequest */ \"b\"].SET_STATE, 'restart');\n      }\n\n      setLoading(false);\n      setVisible(false);\n    }).catch(error => {\n      console.error(error);\n      clearAndAddHttpError({\n        key: 'feature:eula',\n        error\n      });\n    }).then(() => setLoading(false));\n  };\n\n  Object(react__WEBPACK_IMPORTED_MODULE_1__[\"useEffect\"])(() => {\n    clearFlashes('feature:eula');\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_components_elements_Modal__WEBPACK_IMPORTED_MODULE_3__[/* default */ \"b\"], {\n    visible: visible,\n    onDismissed: () => setVisible(false),\n    closeOnBackground: false,\n    showSpinnerOverlay: loading\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledFlashMessageRender, {\n    key: 'feature:eula',\n    \"data-tw\": \"mb-4\"\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledH, {\n    \"data-tw\": \"text-2xl mb-4 text-neutral-100\"\n  }, \"Accept Minecraft\\xAE EULA\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledP, {\n    \"data-tw\": \"text-neutral-200\"\n  }, \"By pressing \", '\"I Accept\"', \" below you are indicating your agreement to the\\xA0\", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledA, {\n    target: '_blank',\n    rel: 'noreferrer noopener',\n    href: \"https://www.minecraft.net/eula\",\n    \"data-tw\": \"text-primary-300 underline transition-colors duration-150 hover:text-primary-400\"\n  }, \"Minecraft\\xAE EULA\"), \".\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledDiv, {\n    \"data-tw\": \"mt-8 sm:flex items-center justify-end\"\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton, {\n    isSecondary: true,\n    onClick: () => setVisible(false),\n    \"data-tw\": \"w-full sm:w-auto border-transparent\"\n  }, \"Cancel\"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_1___default.a.createElement(_StyledButton2, {\n    onClick: onAcceptEULA,\n    \"data-tw\": \"mt-4 sm:mt-0 sm:ml-4 w-full sm:w-auto\"\n  }, \"I Accept\")));\n};\n\n__signature__(EulaModalFeature, \"useState{[visible, setVisible](false)}\\nuseState{[loading, setLoading](false)}\\nuseStoreState{uuid}\\nuseStoreState{status}\\nuseFlash{{ clearFlashes, clearAndAddHttpError }}\\nuseStoreState{{ connected, instance }}\\nuseEffect{}\\nuseEffect{}\", () => [_plugins_useFlash__WEBPACK_IMPORTED_MODULE_7__[/* default */ \"a\"]]);\n\nconst _default = EulaModalFeature;\n/* harmony default export */ __webpack_exports__[\"default\"] = (_default);\n\nvar _StyledFlashMessageRender = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_FlashMessageRender__WEBPACK_IMPORTED_MODULE_6__[/* default */ \"a\"]).withConfig({\n  displayName: \"EulaModalFeature___StyledFlashMessageRender\",\n  componentId: \"sc-43378k-0\"\n})({\n  \"marginBottom\": \"1rem\"\n});\n\nvar _StyledH = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"h2\").withConfig({\n  displayName: \"EulaModalFeature___StyledH\",\n  componentId: \"sc-43378k-1\"\n})({\n  \"fontSize\": \"1.5rem\",\n  \"lineHeight\": \"2rem\",\n  \"marginBottom\": \"1rem\",\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(214, 15%, 91%, var(--tw-text-opacity))\"\n});\n\nvar _StyledP = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"p\").withConfig({\n  displayName: \"EulaModalFeature___StyledP\",\n  componentId: \"sc-43378k-2\"\n})({\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"hsla(210, 16%, 82%, var(--tw-text-opacity))\"\n});\n\nvar _StyledA = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"a\").withConfig({\n  displayName: \"EulaModalFeature___StyledA\",\n  componentId: \"sc-43378k-3\"\n})({\n  \"--tw-text-opacity\": \"1\",\n  \"color\": \"rgba(147, 197, 253, var(--tw-text-opacity))\",\n  \"textDecoration\": \"underline\",\n  \"transitionProperty\": \"background-color, border-color, color, fill, stroke\",\n  \"transitionTimingFunction\": \"cubic-bezier(0.4, 0, 0.2, 1)\",\n  \"transitionDuration\": \"150ms\",\n  \":hover\": {\n    \"--tw-text-opacity\": \"1\",\n    \"color\": \"rgba(96, 165, 250, var(--tw-text-opacity))\"\n  }\n});\n\nvar _StyledDiv = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(\"div\").withConfig({\n  displayName: \"EulaModalFeature___StyledDiv\",\n  componentId: \"sc-43378k-4\"\n})({\n  \"marginTop\": \"2rem\",\n  \"alignItems\": \"center\",\n  \"justifyContent\": \"flex-end\",\n  \"@media (min-width: 640px)\": {\n    \"display\": \"flex\"\n  }\n});\n\nvar _StyledButton = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"]).withConfig({\n  displayName: \"EulaModalFeature___StyledButton\",\n  componentId: \"sc-43378k-5\"\n})({\n  \"width\": \"100%\",\n  \"borderColor\": \"rgba(0, 0, 0, 0)\",\n  \"@media (min-width: 640px)\": {\n    \"width\": \"auto\"\n  }\n});\n\nvar _StyledButton2 = Object(styled_components__WEBPACK_IMPORTED_MODULE_0__[/* default */ \"c\"])(_components_elements_Button__WEBPACK_IMPORTED_MODULE_4__[/* default */ \"a\"]).withConfig({\n  displayName: \"EulaModalFeature___StyledButton2\",\n  componentId: \"sc-43378k-6\"\n})({\n  \"marginTop\": \"1rem\",\n  \"width\": \"100%\",\n  \"@media (min-width: 640px)\": {\n    \"marginTop\": \"0px\",\n    \"marginLeft\": \"1rem\",\n    \"width\": \"auto\"\n  }\n});\n\n;\n\n(function () {\n  var reactHotLoader = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.default : undefined;\n\n  if (!reactHotLoader) {\n    return;\n  }\n\n  reactHotLoader.register(EulaModalFeature, \"EulaModalFeature\", \"/workspaces/Pterod/resources/scripts/components/server/features/eula/EulaModalFeature.tsx\");\n  reactHotLoader.register(_default, \"default\", \"/workspaces/Pterod/resources/scripts/components/server/features/eula/EulaModalFeature.tsx\");\n})();\n\n;\n\n(function () {\n  var leaveModule = typeof reactHotLoaderGlobal !== 'undefined' ? reactHotLoaderGlobal.leaveModule : undefined;\n  leaveModule && leaveModule(module);\n})();\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../../../../../node_modules/webpack/buildin/harmony-module.js */ \"./node_modules/webpack/buildin/harmony-module.js\")(module)))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./resources/scripts/components/server/features/eula/EulaModalFeature.tsx\n");

/***/ })

}]);