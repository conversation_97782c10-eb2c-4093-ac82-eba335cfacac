(window["webpackJsonp"] = window["webpackJsonp"] || []).push([["vendors~auth"],{

/***/ "./node_modules/reaptcha/dist/index.js":
/*!*********************************************!*\
  !*** ./node_modules/reaptcha/dist/index.js ***!
  \*********************************************/
/*! no static exports found */
/*! exports used: default */
/***/ (function(module, exports, __webpack_require__) {

eval("/* WEBPACK VAR INJECTION */(function(global) {(function webpackUniversalModuleDefinition(root, factory) {\n\tif(true)\n\t\tmodule.exports = factory(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\n\telse {}\n})(global, function(__WEBPACK_EXTERNAL_MODULE__0__) {\nreturn /******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// define __esModule on exports\n/******/ \t__webpack_require__.r = function(exports) {\n/******/ \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n/******/ \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n/******/ \t\t}\n/******/ \t\tObject.defineProperty(exports, '__esModule', { value: true });\n/******/ \t};\n/******/\n/******/ \t// create a fake namespace object\n/******/ \t// mode & 1: value is a module id, require it\n/******/ \t// mode & 2: merge all properties of value into the ns\n/******/ \t// mode & 4: return value when already ns object\n/******/ \t// mode & 8|1: behave like require\n/******/ \t__webpack_require__.t = function(value, mode) {\n/******/ \t\tif(mode & 1) value = __webpack_require__(value);\n/******/ \t\tif(mode & 8) return value;\n/******/ \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n/******/ \t\tvar ns = Object.create(null);\n/******/ \t\t__webpack_require__.r(ns);\n/******/ \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n/******/ \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n/******/ \t\treturn ns;\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 1);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__0__;\n\n/***/ }),\n/* 1 */\n/***/ (function(module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n__webpack_require__.r(__webpack_exports__);\n\n// EXTERNAL MODULE: external {\"amd\":\"react\",\"commonjs\":\"react\",\"commonjs2\":\"react\",\"root\":\"React\"}\nvar external_amd_react_commonjs_react_commonjs2_react_root_React_ = __webpack_require__(0);\nvar external_amd_react_commonjs_react_commonjs2_react_root_React_default = /*#__PURE__*/__webpack_require__.n(external_amd_react_commonjs_react_commonjs2_react_root_React_);\n\n// CONCATENATED MODULE: ./utils/injectScript.js\n/* harmony default export */ var injectScript = (function (scriptSrc) {\n  var script = document.createElement('script');\n  script.async = true;\n  script.defer = true;\n  script.src = scriptSrc;\n\n  if (document.head) {\n    document.head.appendChild(script);\n  }\n});\n// CONCATENATED MODULE: ./utils/isAnyScriptPresent.js\n/* harmony default export */ var isAnyScriptPresent = (function (regex) {\n  return Array.from(document.scripts).reduce(function (isPresent, script) {\n    return isPresent ? isPresent : regex.test(script.src);\n  }, false);\n});\n// CONCATENATED MODULE: ./index.js\nfunction _typeof(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\n\n\n\nvar RECAPTCHA_SCRIPT_URL = 'https://recaptcha.net/recaptcha/api.js';\nvar RECAPTCHA_SCRIPT_REGEX = /(http|https):\\/\\/(www)?.+\\/recaptcha/;\n\nvar index_Reaptcha =\n/*#__PURE__*/\nfunction (_Component) {\n  _inherits(Reaptcha, _Component);\n\n  function Reaptcha(props) {\n    var _this;\n\n    _classCallCheck(this, Reaptcha);\n\n    _this = _possibleConstructorReturn(this, _getPrototypeOf(Reaptcha).call(this, props));\n\n    _defineProperty(_assertThisInitialized(_this), \"container\", null);\n\n    _defineProperty(_assertThisInitialized(_this), \"_isAvailable\", function () {\n      return Boolean(window && window.grecaptcha && window.grecaptcha.ready);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_inject\", function () {\n      if (_this.props.inject && !isAnyScriptPresent(RECAPTCHA_SCRIPT_REGEX)) {\n        var hlParam = _this.props.hl ? \"&hl=\".concat(_this.props.hl) : '';\n        var src = \"\".concat(RECAPTCHA_SCRIPT_URL, \"?render=explicit\").concat(hlParam);\n        injectScript(src);\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_prepare\", function () {\n      var _this$props = _this.props,\n          explicit = _this$props.explicit,\n          onLoad = _this$props.onLoad;\n      window.grecaptcha.ready(function () {\n        _this.setState({\n          ready: true\n        }, function () {\n          if (!explicit) {\n            _this.renderExplicitly();\n          }\n\n          if (onLoad) {\n            onLoad();\n          }\n        });\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_renderRecaptcha\", function (container, config) {\n      return window.grecaptcha.render(container, config);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_resetRecaptcha\", function () {\n      return window.grecaptcha.reset(_this.state.instanceId);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_executeRecaptcha\", function () {\n      return window.grecaptcha.execute(_this.state.instanceId);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_getResponseRecaptcha\", function () {\n      return window.grecaptcha.getResponse(_this.state.instanceId);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_stopTimer\", function () {\n      if (_this.state.timer) {\n        clearInterval(_this.state.timer);\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"componentDidMount\", function () {\n      _this._inject();\n\n      if (_this._isAvailable()) {\n        _this._prepare();\n      } else {\n        var timer = setInterval(function () {\n          if (_this._isAvailable()) {\n            _this._prepare();\n\n            _this._stopTimer();\n          }\n        }, 500);\n\n        _this.setState({\n          timer: timer\n        });\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"shouldComponentUpdate\", function (nextProps) {\n      return _this.props.className !== nextProps.className || !_this.state.rendered;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"componentWillUnmount\", function () {\n      _this._stopTimer();\n\n      if (_this.state.rendered) {\n        _this._resetRecaptcha();\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"renderExplicitly\", function () {\n      return new Promise(function (resolve, reject) {\n        if (_this.state.rendered) {\n          return reject(new Error('This recaptcha instance has been already rendered.'));\n        }\n\n        if (_this.state.ready && _this.container) {\n          var instanceId = _this._renderRecaptcha(_this.container, {\n            sitekey: _this.props.sitekey,\n            theme: _this.props.theme,\n            size: _this.props.size,\n            badge: _this.state.invisible ? _this.props.badge : null,\n            tabindex: _this.props.tabindex,\n            callback: _this.props.onVerify,\n            'expired-callback': _this.props.onExpire,\n            'error-callback': _this.props.onError,\n            isolated: _this.state.invisible ? _this.props.isolated : null,\n            hl: _this.state.invisible ? null : _this.props.hl\n          });\n\n          _this.setState({\n            instanceId: instanceId,\n            rendered: true\n          }, function () {\n            if (_this.props.onRender) {\n              _this.props.onRender();\n            }\n\n            resolve();\n          });\n        } else {\n          return reject(new Error('Recaptcha is not ready for rendering yet.'));\n        }\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"reset\", function () {\n      return new Promise(function (resolve, reject) {\n        if (_this.state.rendered) {\n          _this._resetRecaptcha();\n\n          return resolve();\n        }\n\n        reject(new Error('This recaptcha instance did not render yet.'));\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"execute\", function () {\n      return new Promise(function (resolve, reject) {\n        if (!_this.state.invisible) {\n          return reject(new Error('Manual execution is only available for invisible size.'));\n        }\n\n        if (_this.state.rendered) {\n          _this._executeRecaptcha();\n\n          resolve();\n        }\n\n        return reject(new Error('This recaptcha instance did not render yet.'));\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"getResponse\", function () {\n      return new Promise(function (resolve, reject) {\n        if (_this.state.rendered) {\n          var _response = _this._getResponseRecaptcha();\n\n          return resolve(_response);\n        }\n\n        reject(new Error('This recaptcha instance did not render yet.'));\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"render\", function () {\n      var container = external_amd_react_commonjs_react_commonjs2_react_root_React_default.a.createElement(\"div\", {\n        id: _this.props.id,\n        className: _this.props.className,\n        ref: function ref(e) {\n          return _this.container = e;\n        }\n      });\n      return _this.props.children ? _this.props.children({\n        renderExplicitly: _this.renderExplicitly,\n        reset: _this.reset,\n        execute: _this.execute,\n        getResponse: _this.getResponse,\n        recaptchaComponent: container\n      }) : container;\n    });\n\n    _this.state = {\n      instanceId: null,\n      ready: false,\n      rendered: false,\n      invisible: _this.props.size === 'invisible',\n      timer: null\n    };\n    return _this;\n  }\n\n  return Reaptcha;\n}(external_amd_react_commonjs_react_commonjs2_react_root_React_[\"Component\"]);\n\n_defineProperty(index_Reaptcha, \"defaultProps\", {\n  id: '',\n  className: 'g-recaptcha',\n  theme: 'light',\n  size: 'normal',\n  badge: 'bottomright',\n  tabindex: 0,\n  explicit: false,\n  inject: true,\n  isolated: false,\n  hl: ''\n});\n\n/* harmony default export */ var index = __webpack_exports__[\"default\"] = (index_Reaptcha);\n\n/***/ })\n/******/ ])[\"default\"];\n});\n/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./../../webpack/buildin/global.js */ \"./node_modules/webpack/buildin/global.js\")))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/reaptcha/dist/index.js\n");

/***/ })

}]);